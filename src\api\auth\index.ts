import { useMutation } from "@tanstack/react-query";

import {
  loginWithApple,
  loginWithEmail,
  loginWithFacebook,
  loginWithGoogle,
  signUpWithEmail,
} from "@/firebase/authService";
import { handleApiError, handleApiResponse } from "@/utils/axios/axiosHelper";
import { API } from "@/utils/axios";
import { API_ENDPOINTS } from "../endpoints";
import { LoginResponseT, StatusCodes } from "@/types/api";
import { generateUUID } from "@/utils";
import { User } from "firebase/auth";
import { SignUpResT } from "@/types";

export const useEmailSignUp = () => {
  return useMutation({
    mutationFn: ({ email, password }: { email: string; password: string }) =>
      signUpWithEmail(email, password),
  });
};

export const useEmailLogin = () => {
  return useMutation({
    mutationFn: ({ email, password }: { email: string; password: string }) =>
      loginWithEmail(email, password),
  });
};

export const useGoogleLogin = () => {
  return useMutation({
    mutationFn: loginWithGoogle,
  });
};

export const useFacebookLogin = () => {
  return useMutation({
    mutationFn: loginWithFacebook,
  });
};
export const useAppleLogin = () => {
  return useMutation({
    mutationFn: loginWithApple,
  });
};

export const useDeleteAccount = () => {
  return useMutation({
    mutationFn: Api_Delete_Account,
  });
};

export const useLoginAccount = () => {
  return useMutation({
    mutationFn: Api_Login_Account,
  });
};

export const useSignUpAccount = () => {
  return useMutation({
    mutationFn: Api_SignUp_Account,
  });
};

const Api_Delete_Account = async (userId: string): Promise<null> => {
  const FailureMessage = "Failed to delete account...";

  const data = { _id: userId };

  try {
    const response = await API.post(API_ENDPOINTS.auth.deleteAccount, data);
    const result = handleApiResponse<null>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_Login_Account = async (data: User): Promise<LoginResponseT> => {
  const FailureMessage = "Failed to login account...";

  const loginData = {
    _id: generateUUID("bizUsr"),
    ...data,
  };

  try {
    const response = await API.post(API_ENDPOINTS.auth.login, loginData);
    const result = handleApiResponse<LoginResponseT>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_SignUp_Account = async (data: User): Promise<SignUpResT> => {
  const FailureMessage = "Failed to signup account...";

  const loginData = {
    _id: generateUUID("bizUsr"),
    ...data,
  };

  try {
    const response = await API.post(API_ENDPOINTS.auth.signUp, loginData);
    const result = handleApiResponse<SignUpResT>(
      response,
      StatusCodes.CREATED,
      FailureMessage
    );

    if (!result.success) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};
