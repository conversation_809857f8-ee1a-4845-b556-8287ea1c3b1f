import { Button } from "@/components/ui/button";
import BussinessInfo from "@/components/common/bussiness-info";
import SideCalendarView from "@/components/common/side-calendar-view";
import NotificaitonsDayWise from "./components/notifications-day-wise";
import EventsOnDateView from "@/components/common/event/events-onDate";
import {
  useGetInfiniteNotifications,
  useMarkNotificationAsRead,
} from "@/api/notification";
import { useAuth } from "@/context/AuthContext";
import { NotificationFormT } from "@/types/api";
import { useNotificationsWithReminders } from "@/hooks/useNotificationsWithReminders";

export default function NotificationsScreen() {
  const { userId } = useAuth();
  const { mutate: markNotificationAsRead } = useMarkNotificationAsRead();

  const payload: Omit<NotificationFormT, "page"> = {
    bizUserId: userId,
    limit: 20,
    sortBy: "date",
    sortOrder: "desc",
  };

  const { data } = useGetInfiniteNotifications(payload);
  const { notifications: reminderNotifications, deleteNotification } = useNotificationsWithReminders();

  const handleMarkAllAsRead = () => {
    const notificationIds = data?.pages.flatMap((page) =>
      page.notifications?.map((notification) => notification._id)
    );

    const validNotificationIds = notificationIds?.filter(
      (id): id is string => id !== undefined
    );

    markNotificationAsRead({
      bizUserId: userId,
      notificationIds: validNotificationIds || [],
    });
  };

  return (
    <div className="flex w-full h-dvh">
      <div className="flex flex-col gap-3 py-3 w-[calc(100%-250px)] h-full">
        <div className="flex w-full justify-between items-end px-6 mt-2">
          <p className="text-base lg:text-xl">Notifications</p>
          <Button
            variant={"ghost"}
            className="h-6 text-sm"
            onClick={handleMarkAllAsRead}>
            Mark All Read
          </Button>
        </div>
        {/* event data */}
        <div className="bg-white w-full h-full flex flex-col p-3 lg:p-6 gap-4 rounded-2xl overflow-y-auto no-scrollbar">
          <NotificaitonsDayWise reminderNotifications={reminderNotifications} deleteNotification={deleteNotification} />
        </div>
      </div>
      {/* events side data */}
      <div className="w-96 lg:w-[450px] px-4 py-3 flex flex-col gap-3  overflow-y-auto">
        {/* bussiness details */}

        <BussinessInfo />

        {/* events */}
        <EventsOnDateView />

        {/* calendar view */}
        <SideCalendarView />
      </div>
    </div>
  );
}
