import { useState, useEffect } from "react";
import { useAuth } from "@/context/AuthContext";
import { processMessageBUT } from "@/api/ai";
import WaveLoader from "@/components/common/ai-app-loader/WaveLoader";
import { EventModelT } from "@/types";
import sendbtn from '@/assets/svg/aisend.svg'
import backgroundImage from '@/assets/images/ai-bg-image.png'
import EniIcon from "@/assets/svg/Eni.svg";
import moment from 'moment';
import { useGetAccounDetails } from "@/api/account";

interface Message {
  text: string;
  sender: "user" | "bot";
  events?: EventModelT[];
  action?: string;
  data?: any;
  needsConfirmation?: boolean;
  originalPrompt?: string;
}

interface AiChatProps {
  onClose: () => void;
}

const AiChat: React.FC<AiChatProps> = ({ onClose }) => {
  const { userId } = useAuth(); // Assuming user object has username/name property
  const { data: accountDetails } = useGetAccounDetails({ userId });
  const [input, setInput] = useState("");
  const [loading, setLoading] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [showWelcome, setShowWelcome] = useState(true);
  const [isInputFocused, setIsInputFocused] = useState(false);

  const userName = accountDetails?.accData?.[0]?.businessName || "there";

  // Welcome messages array
  const welcomeMessages = [
    `Hi ${userName}, how can I help you organize your day?`,
    `Hey ${userName}, ready to schedule something?`,
    `Good to see you, ${userName}! Got anything to plan today?`,
    `Hi ${userName}, what would you like to add to your calendar?`,
    `Hello ${userName}, need help planning your next event?`,
    `Hey ${userName}, let's get your day sorted. What's first?`,
    `Hi ${userName}, I'm here to help manage your time—what's up?`,
    `Welcome back, ${userName}! What's on the agenda today?`,
    `Hi ${userName}, want me to schedule something for you?`,
    `Hi ${userName}, how can I assist with your schedule today?`
  ];

  // Select random welcome message on component mount
  const [welcomeMessage, setWelcomeMessage] = useState(welcomeMessages[0]);

  useEffect(() => {
    setWelcomeMessage(welcomeMessages[Math.floor(Math.random() * welcomeMessages.length)]);
  }, [userName]);

  // Hide welcome message when user starts typing or focuses input
  useEffect(() => {
    if (input.length > 0 || isInputFocused) {
      setShowWelcome(false);
    }
  }, [input, isInputFocused]);

  // Show welcome message again if input is cleared and not focused
  useEffect(() => {
    if (input.length === 0 && !isInputFocused && messages.length === 0) {
      setShowWelcome(true);
    }
  }, [input, isInputFocused, messages.length]);

  const handleSend = async (prompt: string, confirm = false) => {
    if (!prompt.trim() || !userId) return;

    // Hide welcome message when sending message
    setShowWelcome(false);

    if (!confirm) {
      const userMessage: Message = { text: prompt, sender: "user" };
      setMessages((prev) => [...prev, userMessage]);
    }
    setInput("");
    setLoading(true);

    try {
      const response = await processMessageBUT({
        inputSentence: prompt,
        userId: userId,
        confirm: confirm,
      });

      const eventsList = response.data?.events || (Array.isArray(response.data) ? response.data : []);
      
      const isShowEvents = response.data?.action === "show_events" || (response.message.toLowerCase().includes("here are your event") && eventsList.length > 0);

      const hasDataResponse =
        response.data?.response && typeof response.data.response === "string";

      const botMessage: Message = {
        text: hasDataResponse ? response.data.response : response.message,
        sender: "bot",
        events: eventsList,
        action: isShowEvents ? "show_events" : response.data?.action,
        data: response.data,
        needsConfirmation:
          !isShowEvents &&
          ((!confirm &&
            hasDataResponse &&
            response.data.response.startsWith("Preview:")) ||
            (!confirm &&
              response.success &&
              (response.data?.preview || (eventsList && eventsList.length > 0 && !response.message.toLowerCase().includes("here are your event"))))),
        originalPrompt: prompt,
      };
      setMessages((prev) => [...prev, botMessage]);
    } catch (error) {
      const errorMessage: Message = {
        text: "Error processing your request.",
        sender: "bot",
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "No date provided";
    const momentDate = moment(dateString);
    if (!momentDate.isValid()) {
      return "Invalid date";
    }
    return momentDate.format('dddd, MMMM Do YYYY, h:mm A');
  };

  const formatEventDateTime = (event: any) => {
    if (event.date && event.time) {
      return { date: event.date, time: event.time };
    }
    
    if (event.start) {
      const momentDate = moment(event.start);
      if (momentDate.isValid()) {
        const dateStr = momentDate.format('dddd, DD MMM YY');
        const timeStr = momentDate.format('h:mm A');
        return { date: dateStr, time: timeStr };
      }
    }
    
    return { date: "No date provided", time: "No time provided" };
  };

  const formatTimeRange = (start: string, end: string) => {
    console.log('formatTimeRange called with:', { start, end });
    
    try {
      const startMoment = moment(start);
      const endMoment = moment(end);
      
      if (!startMoment.isValid() || !endMoment.isValid()) {
        console.error('Invalid date format');
        return `${start} - ${end}`;
      }
      
      const startTime = startMoment.format('h:mm A');
      const endTime = endMoment.format('h:mm A');
      
      console.log('Formatted times:', { startTime, endTime });
      
      return `${startTime} - ${endTime}`;
    } catch (error) {
      console.error('Error formatting time range:', error);
      return `${start} - ${end}`;
    }
  };

  const renderScheduleEvents = (events: any[]) => {
    if (!events || events.length === 0) return null;

    return (
      <div className="border-2 border-cyan-300 rounded-lg overflow-hidden" style={{ backgroundColor: '#053d4a' }}>
        {events.map((event: any, i) => {
          const { date, time } = formatEventDateTime(event);
          
          return (
            <div key={i} className={`p-4 flex justify-between items-center ${i !== events.length - 1 ? 'border-b border-cyan-300' : ''}`}>
              <div className="flex-1">
                <p className="font-bold text-white text-lg">{event.title || 'Task'}</p>
                <p className="text-gray-200 text-sm">{date}</p>
                <p className="text-gray-200 text-sm">{time}</p>
              </div>
              <div className="text-right">
                <p className="text-gray-200 text-sm font-medium">
                  {event.calendar || event.calendarName || 'Cal Name'}
                </p>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  // Updated function to parse structured bulk update response
  const parseBulkUpdateFromDetails = (details: any) => {
    const rescheduledEvents = details?.successfulUpdates || [];
    const conflictedEvents = details?.conflictedUpdates || [];
    const errorEvents = details?.errorUpdates || [];

    return { 
      rescheduledEvents, 
      conflictedEvents: [...conflictedEvents, ...errorEvents] // Combine conflicts and errors
    };
  };

  // Keep the old function as fallback for text-based responses
  const parseBulkUpdateResponse = (response: string) => {
    const lines = response.split('\n').filter(line => line.trim());
    const rescheduledEvents = [];
    const conflictedEvents = [];
    let parsingRescheduled = false;
    let parsingConflicts = false;

    for (const line of lines) {
      if (line.includes('event(s) will be rescheduled:')) {
        parsingRescheduled = true;
        parsingConflicts = false;
        continue;
      }
      if (line.includes('event(s) had conflicts')) {
        parsingRescheduled = false;
        parsingConflicts = true;
        continue;
      }
      if (line.includes('To save these changes')) {
        break;
      }

      if (parsingRescheduled && line.startsWith('"')) {
        const match = line.match(/"([^"]+)" from ([^ ]+) ([^-]+)-([^ ]+) ([^ ]+) to ([^ ]+) ([^-]+)-([^ ]+) ([^ ]+)/);
        if (match) {
          const [, title, oldDate, oldStartTime, oldEndDate, oldEndTime, newDate, newStartTime, newEndDate, newEndTime] = match;
          rescheduledEvents.push({
            title,
            oldStart: `${oldDate} ${oldStartTime}`,
            oldEnd: `${oldEndDate} ${oldEndTime}`,
            newStart: `${newDate} ${newStartTime}`,
            newEnd: `${newEndDate} ${newEndTime}`,
            start: `${newDate} ${newStartTime}`
          });
        }
      }

      if (parsingConflicts && line.startsWith('"')) {
        const match = line.match(/"([^"]+)" cannot be rescheduled - (.+)/);
        if (match) {
          const [, title, reason] = match;
          conflictedEvents.push({ title, reason });
        }
      }
    }

    return { rescheduledEvents, conflictedEvents };
  };

  const renderRescheduleMessage = (data: any) => {
    console.log('renderRescheduleMessage called with data:', data);
    
    if (data.action === "reschedule") {
      console.log('Old range:', data.oldStart, 'to', data.oldEnd);
      console.log('New range:', data.newStart, 'to', data.newEnd);
      
      return (
        <>
          <div className="mt-2 p-3 rounded-lg border border-cyan-400" style={{ backgroundColor: '#064F5F' }}>
            <p className="text-white text-base">
              {data.eventTitle || data.title || 'Event'} will be rescheduled from {formatTimeRange(data.oldStart, data.oldEnd)} to {formatTimeRange(data.newStart, data.newEnd)}
            </p>
          </div>
          {/* Add confirmation preview block for this single event */}
          {(data.eventTitle || data.title) && data.start && (
            <div className="p-3 my-2 rounded-lg border border-cyan-400" style={{ backgroundColor: '#064F5F' }}>
              <p className="font-bold text-white">{data.eventTitle || data.title}</p>
              <p className="text-gray-300">{formatDate(data.start)}</p>
            </div>
          )}
        </>
      );
    }
    
    // Check if this is a bulk update response
    if (data.action === "bulk_update") {
      // let rescheduledEvents = [];
      let conflictedEvents = [];

      // First try to get data from structured details
      if (data.details) {
        const parsed = parseBulkUpdateFromDetails(data.details);
        // rescheduledEvents = parsed.rescheduledEvents;
        conflictedEvents = parsed.conflictedEvents;
      }
      // Fallback to text parsing if no structured details
      else if (data.response && data.response.includes('event(s) will be rescheduled')) {
        const parsed = parseBulkUpdateResponse(data.response);
        // rescheduledEvents = parsed.rescheduledEvents;
        conflictedEvents = parsed.conflictedEvents;
      }
      
      return (
        <>
          {conflictedEvents.length > 0 && (
            <div className="mt-2 p-3 rounded-lg border border-red-400" style={{ backgroundColor: '#4A1D1D' }}>
              <p className="text-white text-base mb-2 font-semibold">
                {conflictedEvents.length} event(s) had conflicts:
              </p>
              {conflictedEvents.map((conflict: any, i: number) => (
                <p key={i} className="text-red-200 text-sm ml-2 mb-1">
                  • <span className="font-medium">{conflict.title}</span>: {conflict.reason || conflict.error || 'Unknown error'}
                </p>
              ))}
            </div>
          )}
        </>
      );
    }

    return null;
  };

  const renderEventDetails = (event: any) => {
    if (!event) return null;
    
    if (event.action === "update_title") {
      return (
        <div className="mt-2 p-3 rounded-lg border border-cyan-400" style={{ backgroundColor: '#064F5F' }}>
          <p className="text-white">
            <strong>Action:</strong> Update Title
          </p>
          <p className="text-white">
            <strong>Old Title:</strong> {event.oldTitle}
          </p>
          <p className="text-white">
            <strong>New Title:</strong> {event.newTitle}
          </p>
        </div>
      );
    }
    
    if (event.action === "delete") {
      return (
        <div className="mt-2 p-3 rounded-lg border border-cyan-400" style={{ backgroundColor: '#064F5F' }}>
          <p className="text-white">
            <strong>Action:</strong> Delete Event
          </p>
          <p className="text-white">
            <strong>Event:</strong> {event.eventDetails?.title}
          </p>
          <p className="text-white">
            <strong>Time:</strong> {event.eventDetails?.time}
          </p>
        </div>
      );
    }
    
    if (event.title) {
      return (
        <div className="p-3 my-2 rounded-lg border border-cyan-400" style={{ backgroundColor: '#064F5F' }}>
          <p className="font-bold text-white">{event.title}</p>
          <p className="text-gray-300">{formatDate(event.start)}</p>
        </div>
      );
    }
    return null;
  };

  // Function to generate events array for preview display from bulk update data
  const generatePreviewEvents = (data: any) => {
    if (data.action === "bulk_update" && data.details?.successfulUpdates) {
      return data.details.successfulUpdates.map((update: any) => ({
        title: update.title,
        start: update.newStart,
        calendar: 'Updated Event'
      }));
    }
    return [];
  };

  return (
    <div 
      className="ai-chat-container flex flex-col h-full p-4 rounded-lg border relative"
      style={{
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      {/* Dark overlay to make background illustrations barely visible */}
      <div className="absolute inset-0 bg-black bg-opacity-90 rounded-lg"></div>
      
      {/* Content with relative positioning to appear above background */}
      <div className="relative z-10 flex flex-col h-full">
        <div className="bg-gray-900 text-white border-2 border-cyan-400 rounded-lg mb-2">
          <div className="flex items-center px-4 py-3">
            <button
              onClick={onClose}
              className="mr-4 hover:bg-gray-700 p-1 rounded"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
            <div className="flex-1 flex items-center justify-center mr-9">
              <img src={EniIcon} alt="Eni" className="w-9 h-9 mr-2" />
              <h2 className="text-lg font-medium">Eni</h2>
            </div>
          </div>
        </div>
        
        <div className="flex-1 overflow-y-auto mb-4 relative px-2">
          {/* Welcome Message - positioned in the middle of the screen */}
          {showWelcome && messages.length === 0 && (
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <div 
                className={`
                  text-white text-xl font-medium text-center px-6 py-4 rounded-xl
                  bg-gradient-to-br from-cyan-500/20 to-blue-600/20 
                  border border-cyan-400/50 backdrop-blur-sm
                  transform transition-all duration-500 ease-out
                  ${showWelcome ? 'opacity-100 scale-100 translate-y-0' : 'opacity-0 scale-95 translate-y-4'}
                `}
                style={{
                  animation: showWelcome ? 'fadeInUp 0.8s ease-out' : 'fadeOutDown 0.5s ease-in'
                }}
              >
                <div className="mb-2">
                  <img src={EniIcon} alt="Eni" className="w-20 h-20 mx-auto mb-3 opacity-80" />
                </div>
                <p className="leading-relaxed">{welcomeMessage}</p>
                <div className="mt-3 text-cyan-300 text-sm opacity-75">
                  Click below to start chatting...
                </div>
              </div>
            </div>
          )}

          {messages.map((msg, index) => (
            <div key={index}>
              {/* Regular message bubble */}
              <div
                className={`p-4 my-3 max-w-4xl rounded-xl ${
                  msg.sender === "user"
                    ? "bg-slate-800 text-white border-2 border-cyan-400 ml-auto rounded-tr-none"
                    : "text-white border-2 border-cyan-300 rounded-tl-none"
                }`}
                style={msg.sender === "bot" ? { backgroundColor: '#064F5F' } : {}}
              >
                <p className="whitespace-pre-wrap text-base leading-relaxed">{msg.text}</p>
                
                {/* Render reschedule message for reschedule actions */}
                {msg.data && (msg.data.action === "reschedule" || msg.data.action === "bulk_update") && 
                  renderRescheduleMessage(msg.data)}
                
                {/* Render other event details (excluding reschedule which is handled above) */}
                {msg.data &&
                  !msg.events?.length &&
                  msg.data.action !== "reschedule" &&
                  msg.data.action !== "bulk_update" &&
                  renderEventDetails(msg.data)}

                {/* Only show confirmation buttons if there are NO events to display and no bulk update */}
                {msg.needsConfirmation && 
                 (!msg.events || msg.events.length === 0) && 
                 msg.data?.action !== "bulk_update" && (
                  <div className="mt-4">
                    {/* Styled confirmation text box */}
                    <div 
                      className="p-4 rounded-xl text-white border-2 border-cyan-300 rounded-tl-none mb-3"
                      style={{ backgroundColor: '#064F5F' }}
                    >
                      <p className="text-white text-base text-center">
                        Shall I add it to your calendar/events?
                      </p>
                    </div>
                    <div className="flex gap-3">
                      <button
                        onClick={() =>
                          setMessages((prev) => [
                            ...prev,
                            { text: "Cancelled.", sender: "bot" },
                          ])
                        }
                        className="flex-1 py-3 bg-cyan-500 hover:bg-cyan-600 text-white rounded-full font-medium transition-colors"
                      >
                        No
                      </button>
                      <button
                        onClick={() => handleSend(msg.originalPrompt!, true)}
                        className="flex-1 py-3 bg-cyan-500 hover:bg-cyan-600 text-white rounded-full font-medium transition-colors"
                      >
                        Yes
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Schedule display for any AI response with events OR bulk update preview */}
              {msg.sender === "bot" && (
                (msg.events && msg.events.length > 0) || 
                (msg.data?.action === "bulk_update" && msg.data?.details?.successfulUpdates)
              ) && (
                <div className="my-3 max-w-4xl">
                  {/* Add header for rescheduled events */}
                  {(msg.data?.action === "reschedule" || msg.data?.action === "bulk_update") && (
                    <div className="mb-2">
                      <p className="text-white text-base font-medium">New scheduled time:</p>
                    </div>
                  )}
                  
                  {/* Display existing events or generate preview events from bulk update */}
                  {msg.events && msg.events.length > 0 
                    ? renderScheduleEvents(msg.events)
                    : renderScheduleEvents(generatePreviewEvents(msg.data))
                  }
                  
                  {/* Show confirmation buttons AFTER events are displayed for bulk updates */}
                  {msg.needsConfirmation && msg.data?.action === "bulk_update" && (
                    <div className="mt-4 justify-center">
                      {/* Styled confirmation text box */}
                      <div 
                        className="p-4 rounded-xl text-white border-2 border-cyan-300 rounded-tl-none mb-3"
                        style={{ backgroundColor: '#064F5F' }}
                      >
                        <p className="text-white text-base text-center">
                          Shall I add it to your calendar/events?
                        </p>
                      </div>
                      <div className="flex gap-3">
                        <button
                          onClick={() =>
                            setMessages((prev) => [
                              ...prev,
                              { text: "Cancelled.", sender: "bot" },
                            ])
                          }
                          className="flex-1 py-3 bg-cyan-500 hover:bg-cyan-600 text-white rounded-full font-medium transition-colors"
                        >
                          No
                        </button>
                        <button
                          onClick={() => handleSend(msg.originalPrompt!, true)}
                          className="flex-1 py-3 bg-cyan-500 hover:bg-cyan-600 text-white rounded-full font-medium transition-colors"
                        >
                          Yes
                        </button>
                      </div>
                    </div>
                  )}
                  
                  {/* Show confirmation buttons for regular events (non-bulk update) */}
                  {msg.needsConfirmation && msg.data?.action !== "bulk_update" && msg.events && msg.events.length > 0 && (
                    <div className="mt-4 justify-center">
                      {/* Styled confirmation text box */}
                      <div 
                        className="p-4 rounded-xl text-white border-2 border-cyan-300 rounded-tl-none mb-3"
                        style={{ backgroundColor: '#064F5F' }}
                      >
                        <p className="text-white text-base text-center">
                          Shall I add it to your calendar/events?
                        </p>
                      </div>
                      <div className="flex gap-3">
                        <button
                          onClick={() =>
                            setMessages((prev) => [
                              ...prev,
                              { text: "Cancelled.", sender: "bot" },
                            ])
                          }
                          className="flex-1 py-3 bg-cyan-500 hover:bg-cyan-600 text-white rounded-full font-medium transition-colors"
                        >
                          No
                        </button>
                        <button
                          onClick={() => handleSend(msg.originalPrompt!, true)}
                          className="flex-1 py-3 bg-cyan-500 hover:bg-cyan-600 text-white rounded-full font-medium transition-colors"
                        >
                          Yes
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
        
        {/* Loading animation positioned right above the input */}
        {loading && (
          <div className="flex justify-start mb-2 px-2">
            <div 
              className="p-4 max-w-4xl rounded-xl border-2 border-cyan-300 rounded-tl-none"
              style={{ backgroundColor: '#064F5F' }}
            >
              <WaveLoader />
            </div>
          </div>
        )}
        
        <div className="flex items-center bg-[#1e1e24] bg-opacity-80 border-2 border-[#00c8ff] rounded-full px-4 py-2 shadow-md w-full max-w-xl mx-auto">
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyPress={(e) => e.key === "Enter" && handleSend(input)}
            onFocus={() => setIsInputFocused(true)}
            onBlur={() => setIsInputFocused(false)}
            placeholder="Type your command..."
            className="flex-1 bg-transparent text-white placeholder-gray-400 outline-none"
          />
          <button onClick={() => handleSend(input)} className="ml-2 hover:scale-110 transition-transform duration-150">
            <img src={sendbtn} alt="Send" className="w-6 h-6" />
          </button>
        </div>
      </div>
      

    </div>
  );
};

export default AiChat;