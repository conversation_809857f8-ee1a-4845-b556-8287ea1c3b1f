import { Toaster } from "sonner";
import { BrowserRouter, Route, Routes } from "react-router";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

import HomeScreen from "@/screens/main/home";
import SearchScreen from "@/screens/main/search";
import AuthProvider from "@/context/AuthContext";
import LoginScreen from "@/screens/auth/login-in";
import SignUpScreen from "@/screens/auth/singn-up";
import UserProfileScreen from "@/screens/main/profile";
import ProtectedLayout from "@/layout/ProtectedLayout";
import ToDoListScreen from "@/screens/main/to-do-list";
import EditEventScreen from "@/screens/main/edit-event";
import CalendarScreen from "@/screens/main/calendar/main";
import EventDetailsScreen from "@/screens/main/event-details";
import NotificationsScreen from "@/screens/main/notifications";
import SignUpDetailsScreen from "@/screens/auth/sign-up-details";
import AddCalendarScreen from "@/screens/main/calendar/add-calendar";
import EditCalendarScreen from "@/screens/main/calendar/edit-calendar";
import PageUnderConstruction from "@/components/PageUnderConstruction";
import AddEventWithEnterScreen from "@/screens/main/add-events/enter-event";
import CalendarDetailsScreen from "@/screens/main/calendar/calendar-details";
import AddEventWithUploadScreen from "@/screens/main/add-events/upload-event";
import PublicCalendarScreen from "@/screens/main/public-calendar";
import PublicEventDetailsScreen from "@/screens/main/public-calendar/public-event-details";
import AllBusinessCalendarsScreen from "@/screens/main/public-calendar/all-calendars";

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Toaster richColors />
      <BrowserRouter>
        <AuthProvider>
          <Routes>
            {/* Public Route */}
            <Route path="/login" element={<LoginScreen />} />
            <Route path="/signup" element={<SignUpScreen />} />
            <Route path="/signup-details" element={<SignUpDetailsScreen />} />
            <Route path="/calendar/public/:id" element={<PublicCalendarScreen />} />
            <Route path="/calendar/public/:id/event/:eventId/:eventDate" element={<PublicEventDetailsScreen />} />
            <Route path="/calendar/public/all/:userId" element={<AllBusinessCalendarsScreen />} />

            {/* Protected Routes (With Sidebar) */}
            <Route element={<ProtectedLayout />}>
              <Route path="/" element={<HomeScreen />} />
              <Route path="/event" element={<HomeScreen />} />
              <Route path="/event/:id" element={<EventDetailsScreen />} />
              <Route path="/add-event" element={<AddEventWithEnterScreen />} />
              <Route
                path="/edit-event/:eventId"
                element={<EditEventScreen />}
              />
              <Route
                path="/add-event-upload"
                element={<AddEventWithUploadScreen />}
              />
              <Route path="/search" element={<SearchScreen />} />
              <Route path="/calendar" element={<CalendarScreen />} />
              <Route path="/calendar/:id" element={<CalendarDetailsScreen />} />
              <Route
                path="/calendar/add-calendar"
                element={<AddCalendarScreen />}
              />
              <Route
                path="/edit-calendar/:id"
                element={<EditCalendarScreen />}
              />
              <Route path="/notifications" element={<NotificationsScreen />} />
              <Route path="/to-do" element={<ToDoListScreen />} />
              <Route path="/profile" element={<UserProfileScreen />} />
              <Route path="*" element={<PageUnderConstruction />} />
            </Route>
          </Routes>
        </AuthProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
}

export default App;
