// import { useState, useRef, useEffect, useCallback } from 'react';
// import { BsBell, BsBellFill, BsCalendarEvent } from 'react-icons/bs';
// import { useNotificationsWithReminders } from '../hooks/useNotificationsWithReminders';
// import DebugReminder from './DebugReminder';
// import './NotificationBell.css';

// interface BellNotification {
//   id: string;
//   title: string;
//   message: string;
//   timestamp: string;
//   read: boolean;
//   type?: 'reminder' | 'general';
//   eventId?: string;
//   avatar?: string;
// }

// const NotificationBell = () => {
//   console.log('[NotificationBell] Component rendered/re-rendered.');
//   const {
//     notifications,
//     markAsRead,
//     markAllAsRead,
//     deleteNotification,
//     requestNotificationPermission,
//     notificationPermission
//   } = useNotificationsWithReminders();
  
//   const [isOpen, setIsOpen] = useState(false);
//   const dropdownRef = useRef<HTMLDivElement>(null);
//   const bellRef = useRef<HTMLDivElement>(null);

//   const unreadCount = notifications.filter(n => !n.read).length;

//   useEffect(() => {
//     const handleClickOutside = (event: MouseEvent) => {
//       if (
//         dropdownRef.current && 
//         !dropdownRef.current.contains(event.target as Node) &&
//         bellRef.current && 
//         !bellRef.current.contains(event.target as Node)
//       ) {
//         setIsOpen(false);
//       }
//     };

//     document.addEventListener('mousedown', handleClickOutside);
//     return () => document.removeEventListener('mousedown', handleClickOutside);
//   }, []);

//   const getNotificationIcon = (notification: BellNotification) => {
//     if (notification.type === 'reminder') {
//       return <BsCalendarEvent className="notification-type-icon reminder" />;
//     }
//     return null;
//   };

//   const toggleDropdown = useCallback(() => {
//     setIsOpen(prev => !prev);
//   }, []);

//   return (
//     <div className="notification-bell-container">
//       <div className="notification-bell-icon" onClick={toggleDropdown} ref={bellRef}>
//         {unreadCount > 0 ? (
//           <BsBellFill size={24} color="#FFD700" />
//         ) : (
//           <BsBell size={24} color="#555" />
//         )}
        
//         {unreadCount > 0 && (
//           <span className="notification-badge">{unreadCount}</span>
//         )}
//       </div>

//       {isOpen && (
//         <div className="notification-dropdown" ref={dropdownRef}>
//           <div className="notification-header">
//             <h3>Notifications</h3>
//             {unreadCount > 0 && (
//               <button onClick={markAllAsRead} className="mark-all-read-btn">
//                 Mark all as read
//               </button>
//             )}
//           </div>
//           <div className="notification-list">
//             {notificationPermission !== 'granted' && (
//               <div className="notification-permission-prompt">
//                 <p>Enable browser notifications for reminders.</p>
//                 <button onClick={requestNotificationPermission} className="request-permission-btn">
//                   Enable Notifications
//                 </button>
//               </div>
//             )}
//             {notifications.length === 0 && notificationPermission === 'granted' ? (
//               <p className="no-notifications">No notifications</p>
//             ) : (
//               notifications.map((notification) => (
//                 <div 
//                   key={notification.id} 
//                   className={`notification-item ${notification.read ? '' : 'unread'} ${notification.type === 'reminder' ? 'reminder-notification' : ''}`}
//                   onClick={() => markAsRead(notification.id)}
//                 >
//                   <div className="notification-avatar">
//                     {notification.type === 'reminder' ? (
//                       <img src="/icons/reminder.png" alt="Reminder Icon" />
//                     ) : (
//                       <img src={notification.avatar || '/icons/default.png'} alt="User Avatar" />
//                     )}
//                   </div>
//                   <div className="notification-content">
//                     <div className="notification-title">
//                       {getNotificationIcon(notification)}
//                       {notification.title}
//                     </div>
//                     <p className="notification-message">{notification.message}</p>
//                     <span className="notification-timestamp">{notification.timestamp}</span>
//                   </div>
//                   <button 
//                     className="notification-delete-btn"
//                     onClick={(
//                       e
//                     ) => {
//                       e.stopPropagation();
//                       deleteNotification(notification.id);
//                     }}
//                   >
//                     ×
//                   </button>
//                   {!notification.read && <div className="notification-unread-dot"></div>}
//                 </div>
//               ))
//             )}
//           </div>
//           {process.env.NODE_ENV === 'development' && <DebugReminder />}
//         </div>
//       )}
//     </div>
//   );
// };

// export default NotificationBell;