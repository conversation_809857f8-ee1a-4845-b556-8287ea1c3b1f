// src/types/reminder.ts
export interface ScheduledReminder {
  id: string;
  eventId: string;
  eventTitle: string;
  eventDateTime: string;
  reminderTime: string; // e.g., "15-min"
  triggerAt: number; // timestamp
  timeoutId?: number;
}

export interface ReminderNotification {
  id: string;
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  type: 'reminder';
  eventId: string;
}

export const REMINDER_TIME_OPTIONS = {
  '5-min': { label: '5 Minutes', minutes: 5 },
  '10-min': { label: '10 Minutes', minutes: 10 },
  '15-min': { label: '15 Minutes', minutes: 15 },
  '30-min': { label: '30 Minutes', minutes: 30 },
  '1-hour': { label: '1 Hour', minutes: 60 },
  '2-hours': { label: '2 Hours', minutes: 120 },
  '1-day': { label: '1 Day', minutes: 1440 },
  '2-days': { label: '2 Days', minutes: 2880 },
  '1-week': { label: '1 Week', minutes: 10080 }
};
