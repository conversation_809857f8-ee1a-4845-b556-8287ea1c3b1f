import { Query_Key } from "@/constant/data";
import {
  NotificatinMarkAsReadFormT,
  NotificationFormT,
  NotificationResponseT,
  StatusCodes,
} from "@/types/api";
import { handleApiError, handleApiResponse } from "@/utils/axios/axiosHelper";
import {
  useInfiniteQuery,
  useMutation,
  useQueryClient,
} from "@tanstack/react-query";
import { API_ENDPOINTS } from "../endpoints";
import { API } from "@/utils/axios";

export const useGetInfiniteNotifications = (
  basePayload: Omit<NotificationFormT, "page">
) => {
  return useInfiniteQuery<NotificationResponseT>({
    queryKey: [Query_Key.allNotifications],
    queryFn: ({ pageParam = 1 }) =>
      Api_Get_Notifications({
        ...basePayload,
        page: pageParam as number,
      }),
    getNextPageParam: (lastPage) => {
      const { currentPage, totalPages } = lastPage.pagination;
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
    enabled: !!basePayload.bizUserId,
    staleTime: 60000,
    refetchInterval: 10000,
    initialPageParam: 1,
  });
};

export const useMarkNotificationAsRead = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: Api_Mark_Notification_As_Read,
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: [Query_Key.allNotifications] });
    },
  });
};

const Api_Get_Notifications = async (
  payload: NotificationFormT
): Promise<NotificationResponseT> => {
  const FailureMessage = "Failed to get notifications...";

  try {
    const response = await API.post(API_ENDPOINTS.notification.getAll, payload);
    const result = handleApiResponse<NotificationResponseT>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success || !result.data) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_Mark_Notification_As_Read = async (
  data: NotificatinMarkAsReadFormT
): Promise<null> => {
  const FailureMessage = "Failed to mark notification as read...";

  try {
    const response = await API.post(
      API_ENDPOINTS.notification.markAsRead,
      data
    );
    const result = handleApiResponse<null>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};
