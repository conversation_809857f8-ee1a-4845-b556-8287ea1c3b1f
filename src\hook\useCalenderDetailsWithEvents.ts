import { useMemo } from "react";

// import { EventModelT } from "@/types";
import { useGetSingleCalenderEvents } from "@/api/event";
import { expandRecurringEvents, mapRawEventsToModel } from "@/utils/model";
import { useGetAllCalendar } from "@/api/calendar";
import { convertEventTimesToLocal, filterUpcomingEvents } from "@/utils";

export const useCalendarDetailsWithEvents = ({
  calendarId,
  usrId,
}: {
  calendarId?: string;
  usrId?: string | null;
}) => {
  const { data: calendars, isLoading: loadingCalendars } = useGetAllCalendar({
    usrId: usrId || undefined,
  });

  const calendar = useMemo(() => {
    return calendars?.find((calendar) => calendar._id === calendarId);
  }, [calendars, calendarId]);

  const calendarIds = useMemo(() => {
    return calendar?._id ? [calendar._id] : [];
  }, [calendar]);

  const { data: events, isLoading: loadingEvents } = useGetSingleCalenderEvents(
    {
      calendarIds,
    }
  );

  const { upComingEvents, LocalTimeZoneEvents } = useMemo(() => {
    if (!events) {
      return { upComingEvents: [], LocalTimeZoneEvents: [] };
    }

    const localEvents = convertEventTimesToLocal(mapRawEventsToModel(events));
    const allEvents = expandRecurringEvents(localEvents);
    const upcoming = filterUpcomingEvents(allEvents);

    return { upComingEvents: upcoming, LocalTimeZoneEvents: allEvents };
  }, [events]);

  return {
    apiCalendars: calendar,
    loading: loadingCalendars || loadingEvents,
    events: LocalTimeZoneEvents,
    upComingEvents,
  };
};