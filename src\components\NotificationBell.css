/* Reminder-specific styles */
.reminder-notification {
  border-left: 4px solid #25d366;
}

.reminder-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #25d366, #20b358);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.notification-type-icon {
  margin-right: 6px;
  font-size: 12px;
}

.notification-type-icon.reminder {
  color: #25d366;
}

/* Animation for new notifications */
.notification-item.unread {
  animation: slideInFromRight 0.3s ease-out;
}

 @keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.notification-permission-prompt {
  padding: 10px;
  background-color: #f0f0f0;
  border-radius: 5px;
  margin-bottom: 10px;
  text-align: center;
}

.notification-permission-prompt p {
  margin-bottom: 5px;
  font-size: 0.9em;
  color: #333;
}

.request-permission-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85em;
  transition: background-color 0.2s ease-in-out;
}

.request-permission-btn:hover {
  background-color: #0056b3;
}

.reminder-notification-card {
  border: 1px solid #25d366;
  background-color: #e6ffe6;
}
