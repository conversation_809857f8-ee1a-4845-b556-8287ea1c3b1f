import { useState } from "react";

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { FileWithPreview } from "@/types";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import FileIcon from "@/assets/svg/file-icon.svg";
import InfoIcon from "@/assets/svg/Info-circle-icon.svg";
import { PdfIcon } from "@/assets/svg/icons";
import { useCalendarsWithEvents } from "@/hook/useCalendarsWithEvents";
import { useAuth } from "@/context/AuthContext";
import { useCustomDropzone, FILE_TYPES } from "@/hook/useCustomDropzone";

export function UploadEventFileDialog({
  setConfomation,
}: {
  setConfomation: ({
    title,
    subTitle,
  }: {
    title: string;
    subTitle: string;
  }) => void;
}) {
  const [openDialog, setOpenDialog] = useState(false);
  const { userId } = useAuth();

  const { apiCalendars } = useCalendarsWithEvents(userId);

  const [file, setFile] = useState<FileWithPreview[]>([]);
  const [calendar, setCalendar] = useState("");

  const { getRootProps, getInputProps } = useCustomDropzone({
    acceptedFileTypes: FILE_TYPES.EXCEL,
    maxFiles: 1,
    maxSize: 1 * 1024 * 1024, // 1MB
    multiple: false,
    showToastOnError: true,
    customErrorMessages: {
      "file-invalid-type": "Please upload only Excel or CSV files",
      "file-too-large": "File is too large. Maximum size is 1MB",
      "too-many-files": "You can only upload one file at a time",
    },
    onFilesAccepted: (acceptedFiles) => {
      setFile(
        acceptedFiles.map((file) =>
          Object.assign(file, {
            preview: URL.createObjectURL(file),
          })
        )
      );
    },
    // No onFilesRejected - errors handled automatically
    additionalOptions: {
      noClick: true, // Disable click to open file dialog
    },
  });

  const IsBothValueAvailable = file.length > 0 && calendar.length > 0;

  const handleUploadData = () => {
    setOpenDialog(false);
    setFile([]);
    setCalendar("");
    setConfomation({
      title: "All the events are created in calendar ",
      subTitle: "Calendar name",
    });
  };

  return (
    <Dialog open={openDialog} onOpenChange={setOpenDialog}>
      <DialogTrigger asChild>
        <Button
          size={"sm"}
          variant={"default"}
          className="text-white rounded-full h-8 w-full bg-fb-bPrime-500 drop-shadow-buttonShadow text-sm">
          <PdfIcon className="size-6 text-white" /> Upload Events File
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[525px] !rounded-xl">
        <DialogHeader>
          <DialogTitle></DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
        <div className="flex flex-col gap-3 py-4">
          <p className="text-center text-2xl leading-7 font-semibold">
            Upload your file here (.xl or CSV)
          </p>
          {file.length === 0 ? (
            <div
              {...getRootProps({ className: "dropzone" })}
              className="flex flex-col min-h-56 justify-center items-center px-6 py-4 gap-2 bg-fb-neutral-50 rounded-xl drop-shadow-calendarShadow shadow-calendarInnerShadow">
              <input {...getInputProps()} />
              <p className="text-center text-base leading-7 font-semibold">
                Select file from device or drag n drop your file
              </p>
              <Button
                size={"sm"}
                onClick={(e) => {
                  e.stopPropagation(); // Prevent event conflicts
                  open(); // Trigger file selection
                }}
                variant={"default"}
                className="text-white rounded-full h-8 w-full bg-fb-bPrime-500 drop-shadow-buttonShadow">
                <PdfIcon className="size-6 text-white" />
                Upload Events File
              </Button>
            </div>
          ) : (
            <div className="flex flex-col min-h-56 justify-center items-center px-6 py-4 gap-2 bg-fb-neutral-50 rounded-xl drop-shadow-calendarShadow shadow-calendarInnerShadow">
              <div className="flex flex-col items-center justify-center">
                <img src={FileIcon} alt="" />
                <p className="font-semibold text-sm">{file[0].name}</p>
              </div>
              <Button
                size={"sm"}
                variant={"default"}
                onClick={() => {
                  setFile([]);
                }}
                className="text-black hover:bg-slate-200 rounded-full h-8 w-full bg-fb-bPrime-50 drop-shadow-buttonShadow">
                <img src={FileIcon} alt="" className="w-6 h-6" /> Change File
              </Button>
            </div>
          )}

          {file.length > 0 && (
            <div className="flex flex-col justify-center items-center px-6 py-4 gap-6 bg-fb-neutral-50 rounded-xl drop-shadow-calendarShadow shadow-calendarInnerShadow">
              <div className="flex flex-col w-full">
                <Label
                  htmlFor="calendar"
                  className="text-black text-sm font-normal px-4 flex gap-0.5 text-center">
                  Choose the calendar*{" "}
                  <img src={InfoIcon} alt="" className="w-3.5 h-3.5" />
                </Label>
                <Select value={calendar} onValueChange={(e) => setCalendar(e)}>
                  <SelectTrigger className="w-full border-none text-xs lg:text-sm h-7 lg:h-8">
                    <SelectValue placeholder="Select from dropdown" />
                  </SelectTrigger>
                  <SelectContent className="max-h-48">
                    {apiCalendars?.map((cal, ind) => (
                      <SelectItem value={cal._id} key={ind}>
                        {cal.calendarName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button
            onClick={handleUploadData}
            disabled={!IsBothValueAvailable}
            className={cn(
              "w-full  rounded-full drop-shadow-buttonShadow  h-8",
              IsBothValueAvailable
                ? "bg-fb-bPrime-500 text-white "
                : "bg-fb-neutral-50 hover:bg-slate-100 text-black"
            )}>
            Upload
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
