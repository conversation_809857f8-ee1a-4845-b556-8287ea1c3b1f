import { useGetAccounDetails } from "@/api/account";
import { useAuth } from "@/context/AuthContext";

export default function BussinessInfo() {
  const { userId } = useAuth();

  const { data: AccountData } = useGetAccounDetails({
    userId: userId,
  });

  return (
    <div className="px-3 pb-1 flex flex-col items-end w-full border-b border-black h-10 lg:h-12">
      <p className="text-sm md:text-base lg:text-base font-extrabold text-black">
        {AccountData?.accData?.[0]?.businessName}
      </p>
      <p className="text-xs font-light text-black">
        {AccountData?.accData?.[0]?.businessId ||
          AccountData?.accData?.[0]?.email}
      </p>
    </div>
  );
}
