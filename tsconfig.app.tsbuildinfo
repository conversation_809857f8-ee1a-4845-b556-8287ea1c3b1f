{"root": ["./src/App.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/api/account/index.ts", "./src/api/ai/index.ts", "./src/api/auth/index.ts", "./src/api/calendar/index.ts", "./src/api/endpoints/index.ts", "./src/api/event/action.ts", "./src/api/event/index.ts", "./src/api/helper/index.ts", "./src/api/ics/index.ts", "./src/api/notification/index.ts", "./src/api/to-do/index.ts", "./src/assets/svg/icons.tsx", "./src/components/DebugReminder.tsx", "./src/components/NotificationBell.tsx", "./src/components/PageUnderConstruction.tsx", "./src/components/ai/AiChat.tsx", "./src/components/calendar/calendar-tab-switch.tsx", "./src/components/calendar/month-calendar-date-view.tsx", "./src/components/calendar/month-calendar.tsx", "./src/components/calendar/upcoming-events.tsx", "./src/components/calendar/weekly-calendar.tsx", "./src/components/calendar/year-calendar.tsx", "./src/components/common/add-event-small/index.tsx", "./src/components/common/add-event-small/enter-event/index.tsx", "./src/components/common/add-event-small/upload-event/index.tsx", "./src/components/common/add-event-small/upload-event/components/upload-event-file.tsx", "./src/components/common/ai-app-loader/WaveLoader.tsx", "./src/components/common/alert-popup/index.tsx", "./src/components/common/app-loader/index.tsx", "./src/components/common/bussiness-info/index.tsx", "./src/components/common/conformation-popup/index.tsx", "./src/components/common/date-time-picker/index.tsx", "./src/components/common/delete-profile-review-popup/index.tsx", "./src/components/common/event/card-view/index.tsx", "./src/components/common/event/date-change-comp/index.tsx", "./src/components/common/event/date-wise-group-events/index.tsx", "./src/components/common/event/events-on-month/index.tsx", "./src/components/common/event/events-onDate/index.tsx", "./src/components/common/mood-calendar/index.tsx", "./src/components/common/mood-note-blue/index.tsx", "./src/components/common/sad-calendar/index.tsx", "./src/components/common/side-calendar-view/index.tsx", "./src/components/common/time-picker-12h/index.tsx", "./src/components/common/time-picker-12h/input.tsx", "./src/components/common/time-picker-12h/select.tsx", "./src/components/common/time-picker-12h/utils.ts", "./src/components/common/timezon-list/index.tsx", "./src/components/ui/accordion.tsx", "./src/components/ui/avatar.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/button.tsx", "./src/components/ui/calendar.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/command.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/image.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/multi-select.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/select.tsx", "./src/components/ui/separator.tsx", "./src/components/ui/switch.tsx", "./src/components/ui/textarea.tsx", "./src/constant/data.ts", "./src/constant/dummy.ts", "./src/constant/routes.ts", "./src/containers/SideBar.tsx", "./src/context/AuthContext.tsx", "./src/enums/index.ts", "./src/envs/index.ts", "./src/firebase/authService.ts", "./src/firebase/index.ts", "./src/hook/useCalendarsWithEvents.ts", "./src/hook/useCalenderDetailsWithEvents.ts", "./src/hook/useCustomDropzone.ts", "./src/hooks/useEventWithReminders.ts", "./src/hooks/useNotificationsWithReminders.ts", "./src/layout/ProtectedLayout.tsx", "./src/lib/utils.ts", "./src/screens/auth/login-in/index.tsx", "./src/screens/auth/sign-up-details/index.tsx", "./src/screens/auth/singn-up/index.tsx", "./src/screens/main/add-events/components/upload-event-file.tsx", "./src/screens/main/add-events/enter-event/index.tsx", "./src/screens/main/add-events/upload-event/index.tsx", "./src/screens/main/calendar/add-calendar/index.tsx", "./src/screens/main/calendar/calendar-details/index.tsx", "./src/screens/main/calendar/edit-calendar/index.tsx", "./src/screens/main/calendar/main/index.tsx", "./src/screens/main/edit-event/index.tsx", "./src/screens/main/event-details/index.tsx", "./src/screens/main/event-details/components/delete-conformation-popup.tsx", "./src/screens/main/event-details/components/event-details.tsx", "./src/screens/main/home/<USER>", "./src/screens/main/notifications/index.tsx", "./src/screens/main/notifications/components/notifications-day-wise.tsx", "./src/screens/main/profile/index.tsx", "./src/screens/main/public-calendar/all-calendars.tsx", "./src/screens/main/public-calendar/index.tsx", "./src/screens/main/public-calendar/public-event-details.tsx", "./src/screens/main/search/index.tsx", "./src/screens/main/to-do-list/index.tsx", "./src/screens/main/to-do-list/components/AddToDoTask.tsx", "./src/screens/main/to-do-list/components/UpdateTodoTask.tsx", "./src/services/ReminderScheduler.ts", "./src/types/api.ts", "./src/types/index.ts", "./src/types/reminder.ts", "./src/utils/index.ts", "./src/utils/reminderUtils.ts", "./src/utils/axios/axiosHelper.ts", "./src/utils/axios/index.ts", "./src/utils/model/index.ts"], "version": "5.6.3"}