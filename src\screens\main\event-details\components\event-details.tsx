import { BookmarkMinus, Flag, Heart, MapPin, SendHorizonal, Trash2 } from "lucide-react";
import { toast } from "sonner";
import { useNavigate } from "react-router";

import EditIcon from "@/assets/svg/edit-icon.svg";
import MessageIcon from "@/assets/svg/message-icon.svg";
import NoImagePlaceholder from "@/assets/images/No-Image-Placeholder.svg.png";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useState } from "react";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { CommentModelT, EventModelT, SubCommentModelT } from "@/types";
import { UserDummyEventsLike } from "@/constant/dummy";
import { useGetAccounDetails } from "@/api/account";
import { useAuth } from "@/context/AuthContext";
import {
  useCreateComment,
  useCreateSubComment,
  useDeleteComment,
  useDeleteSubComment,
  useReportComment,
  useReportSubComment,
} from "@/api/event/action";
import { CreateCommentFormT, CreateSubCommentFormT } from "@/types/api";
import { generateUUID, formatTimeOnly } from "@/utils";
import { useUpdateEvent } from "@/api/event";
import { DeleteConformationPopup } from "./delete-conformation-popup";

export default function EventDetailsView({ data, eventDate, isPublicView = false }: { data: EventModelT | null; eventDate?: string | null; isPublicView?: boolean }) {
  const [currentView, setCurrentView] = useState("Comments");
  const navigation = useNavigate();
  const [mobilePublicView, setMobilePublicView] = useState("Details");

  const { mutate: deleteEvent, isPending } = useUpdateEvent();

  const [showDeleteAlert, setShowDeleteAlert] = useState(false);

  const handlePreDelete = () => {
    // Check if this is a recurring event (frequency != 'Never')
    if (data?.frequency && data.frequency !== 'Never') {
      setShowDeleteAlert(true);
    } else {
      handleDelete();
    }
  };

  const thisEventOnly = () => {
    if (!data?._id || !eventDate) return;
    
    // Add current event date to restrict array
    const updatedData = {
      ...data,
      restrict: [...(data.restrict || []), eventDate]
    };
    
    deleteEvent(
      { _id: data._id, restrict: updatedData.restrict },
      {
        onSuccess() {
          toast.success("Event removed from this date");
          navigation(-1);
          setShowDeleteAlert(false);
        },
        onError(error: Error) {
          toast.error(error.message);
          setShowDeleteAlert(false);
        },
      }
    );
  };

  const thisAndFutureEvents = () => {
    if (!data?._id || !eventDate) return;
    
    // Set repeat end date to one day before current event
    const endDate = new Date(eventDate);
    endDate.setDate(endDate.getDate() - 1);
    
    deleteEvent(
      { _id: data._id, repeatEndDate: endDate.toISOString() },
      {
        onSuccess() {
          toast.success("This and future events deleted");
          navigation(-1);
          setShowDeleteAlert(false);
        },
        onError(error: Error) {
          toast.error(error.message);
          setShowDeleteAlert(false);
        },
      }
    );
  };

  const handleDelete = () => {
    deleteEvent(
      { _id: data?._id || "", deleted: true },
      {
        onSuccess() {
          toast.success("Event deleted successfully");
          navigation(-1);
          setShowDeleteAlert(false);
        },
        onError(error: Error) {
          toast.error(error.message);
          setShowDeleteAlert(false);
        },
      }
    );
  };

  let startDate: Date | null = null;
  let endDate: Date | null = null;

  if (data) {
    const startOfOriginal = new Date(data.start);
    const endOfOriginal = new Date(data.end);
    const duration = endOfOriginal.getTime() - startOfOriginal.getTime();

    if (eventDate) {
      startDate = new Date(eventDate);
      startDate.setUTCHours(
        startOfOriginal.getUTCHours(),
        startOfOriginal.getUTCMinutes(),
        startOfOriginal.getUTCSeconds(),
        startOfOriginal.getUTCMilliseconds()
      );
      endDate = new Date(startDate.getTime() + duration);
    } else {
      startDate = startOfOriginal;
      endDate = endOfOriginal;
    }
  }

  

  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);

  const handleDeleteEvent = () => {
    deleteEvent(
      { _id: data?._id || "", deleted: true },
      {
        onSuccess() {
          toast.success("Event deleted successfully");
          navigation(-1);
        },
        onError(error: Error) {
          toast.error(error.message);
        },
      }
    );
  };

  return (
<div className="flex flex-col gap-4 w-full pt-1" key={data?._id}>
    {/* Mobile Layout: Event Title at top */}
    <div className="md:hidden">
      <p className="font-semibold text-xl -tracking-wide leading-6 mb-4">
        {data?.title}
      </p>
      
      {/* Mobile Layout: Event Image */}
      <div className="w-full h-64 rounded-lg overflow-hidden mb-4">
        <img
          src={data?.picture || NoImagePlaceholder}
          alt=""
          className="w-full h-full object-cover"
        />
      </div>
      
      {/* Mobile Layout: Tab Navigation */}
      {isPublicView && (
        <div className="flex border-b border-gray-300 mb-4">
          <Button
            variant="ghost"
            className={`flex-1 justify-center rounded-none ${mobilePublicView === 'Details' ? 'border-b-2 border-primary text-primary' : ''}`}
            onClick={() => setMobilePublicView('Details')}
          >
            Details
          </Button>
          <Button
            variant="ghost"
            className={`flex-1 justify-center rounded-none ${mobilePublicView === 'Responses' ? 'border-b-2 border-primary text-primary' : ''}`}
            onClick={() => setMobilePublicView('Responses')}
          >
            Responses
          </Button>
        </div>
      )}
    </div>

    {/* Details Content - Modified for mobile */}
    <div className={cn("w-full h-auto flex flex-col md:flex-row gap-4", isPublicView && mobilePublicView !== 'Details' && "hidden md:flex")}>
      <div className="w-full flex flex-col gap-2">
        <div className="flex flex-col gap-2">
          {/* Desktop Title - hidden on mobile since it's at the top */}
          <p className="font-semibold text-xl -tracking-wide leading-3 hidden md:block">
            {data?.title}
          </p>
          <Separator className="bg-fb-bPrime-hgts hidden md:block" />
          
          <div className="flex gap-4 items-center">
            <div className="h-8 w-8 rounded-full overflow-hidden">
              <img
                src={data?.picture || NoImagePlaceholder}
              />
            </div>
            <div className="flex flex-col gap-0.5">
              <p
                className="text-sm tracking-wide leading-4 cursor-pointer hover:underline"
                onClick={() => navigation(`/calendar/${data?.calId}`)}
              >
                {data?.calendarName}
              </p>
              <p className="text-xs font-light leading-4">
                {data?.businessName}
              </p>
            </div>
          </div>
        </div>
        
        <div className="flex font-semibold text-lg -tracking-wide">
          {data?.allDayEvent ? (
            <>
               <p>{startDate && formatTimeOnly(startDate)}</p>-
              <p>{endDate && formatTimeOnly(endDate)}</p>
            </>
          ) : (
            <>
              <p>{startDate && formatTimeOnly(startDate)}</p>-
              <p>{endDate && formatTimeOnly(endDate)}</p>
            </>
          )}
        </div>
        
        <div className="flex flex-col gap-1">
          <p className="font-semibold text-sm text-black/75">About Event</p>
          <div
            className="prose"
            dangerouslySetInnerHTML={{
              __html: data?.description || "No description",
            }}
          />
        </div>
        
        <div className="flex flex-col gap-1">
          <div className="flex gap-1 items-center">
            <MapPin className="size-4" />{" "}
            <p className="font-semibold text-base">{data?.location}</p>
          </div>
          {data?.link && data.link.length > 0 && (
            <div className="flex flex-col gap-1 mt-2">
              <p className="font-semibold text-sm text-black/75">Links:</p>
              {data.link.map((url, index) => (
                <a
                  key={index}
                  href={url.startsWith("http") ? url : `http://${url}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 underline text-sm"
                >
                  {url}
                </a>
              ))}
            </div>
          )}
        </div>
        
        {!isPublicView && (
          <div className="flex gap-4 w-full mt-auto">
            <Button
              className="bg-fb-neutral-50 w-full hover:bg-neutral-100 h-8 text-black drop-shadow-buttonShadow"
              onClick={() => navigation(`/edit-event/${data?._id}`, { state: { eventDate } })}
            >
              <img src={EditIcon} alt="" className="w-4 h-4" /> Edit
            </Button>
            <Button
              disabled={isPending}
              className="bg-fb-neutral-50 w-full hover:bg-neutral-100 h-8 text-red-500 drop-shadow-buttonShadow"
              onClick={handlePreDelete}
            >
              <Trash2 className="w-4 h-4 text-red-500" /> Delete
            </Button>
          </div>
        )}
      </div>
      
      {/* Desktop Image - hidden on mobile since it's at the top */}
      <div className="hidden md:block w-full md:w-auto md:min-w-80 h-64 md:h-full md:max-h-80 rounded-lg overflow-hidden">
        <img
          src={data?.picture || NoImagePlaceholder}
          alt=""
          className="w-full h-full object-cover"
        />
      </div>
      
      {/* Delete Alert Modal */}
      {showDeleteAlert && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4 drop-shadow-buttonShadow">
            <h3 className="font-semibold text-xl -tracking-wide leading-6 mb-2">Delete Recurring Event</h3>
            <Separator className="bg-fb-bPrime-hgts mb-4" />
            <p className="text-sm text-black/75 mb-6">
              This is a recurring event. Choose how you want to delete it:
            </p>
            <div className="flex flex-col gap-2">
              <Button
                onClick={thisEventOnly}
                className="bg-fb-neutral-50 w-full hover:bg-neutral-100 h-8 text-black drop-shadow-buttonShadow"
              >
                This Event Only
              </Button>
              <Button
                onClick={thisAndFutureEvents}
                className="bg-fb-neutral-50 w-full hover:bg-neutral-100 h-8 text-black drop-shadow-buttonShadow"
              >
                This and Future Events
              </Button>
              <Button
                onClick={handleDelete}
                className="bg-fb-neutral-50 w-full hover:bg-neutral-100 h-8 text-red-500 drop-shadow-buttonShadow"
              >
                <Trash2 className="w-4 h-4 text-red-500 mr-2" />
                All Events in Series
              </Button>
              <Button
                onClick={() => setShowDeleteAlert(false)}
                className="bg-fb-neutral-50 w-full hover:bg-neutral-100 h-8 text-black drop-shadow-buttonShadow mt-2"
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
    
    {/* Responses Section */}
    <div className={cn("flex flex-col gap-4", isPublicView && mobilePublicView !== 'Responses' && "hidden md:flex")}>
      {/* Statistics - Always show */}
      <div className="flex gap-2 justify-between">
        <div
          className="flex gap-0.5 items-center text-sm cursor-pointer"
          onClick={() => {
            if (!isPublicView) setCurrentView("Comments");
          }}
        >
          <img src={MessageIcon} alt="" className="w-4" />
          {data?.Comments?.length || 0}{" "}
          <p
            className={cn(
              "hidden md:block",
              currentView === "Comments" && !isPublicView
                ? "font-semibold underline"
                : "font-normal"
            )}
          >
            Comments
          </p>
        </div>
        <div
          className="flex gap-0.5 items-center text-sm cursor-pointer"
          onClick={() => {
            if (!isPublicView) setCurrentView("Likes");
          }}
        >
          <Heart className="fill-fb-pink size-4" />
          {data?.Impression?.length || 0}{" "}
          <p
            className={cn(
              "hidden md:block",
              currentView === "Likes" && !isPublicView
                ? "font-semibold underline"
                : "font-normal"
            )}
          >
            Likes{" "}
          </p>
        </div>
        <div
          className="flex gap-0.5 items-center text-sm cursor-pointer"
          onClick={() => {
            if (!isPublicView) setCurrentView("Attending");
          }}
        >
          <BookmarkMinus className="fill-fb-uPrime-300 size-4" />
          {data?.Rsvps?.length || 0}{" "}
          <p
            className={cn(
              "hidden md:block",
              currentView === "Attending" && !isPublicView
                ? "font-semibold underline"
                : "font-normal"
            )}
          >
            Attending{" "}
          </p>
        </div>
        <div
          className="flex gap-0.5 items-center text-sm cursor-pointer"
          onClick={() => {
            if (!isPublicView) setCurrentView("deciding");
          }}
        >
          <BookmarkMinus className="fill-fb-purple size-4" />
          {data?.Rsvps?.length || 0}{" "}
          <p
            className={cn(
              "hidden md:block",
              currentView === "deciding" && !isPublicView
                ? "font-semibold underline"
                : "font-normal"
            )}
          >
            Still deciding{" "}
          </p>
        </div>
        <div
          className="flex gap-0.5 items-center text-sm cursor-pointer"
          onClick={() => {
            if (!isPublicView) setCurrentView("notAttending");
          }}
        >
          <BookmarkMinus className="fill-fb-red-200 size-4" />
          {data?.Rsvps?.length || 0}{" "}
          <p
            className={cn(
              "hidden md:block",
              currentView === "notAttending" && !isPublicView
                ? "font-semibold underline"
                : "font-normal"
            )}
          >
            Not attending
          </p>
        </div>
      </div>

      {/* View Components */}
      {currentView === "Comments" && (
        <CommetsView
          eventId={data?._id || ""}
          comments={data?.Comments || []}
          replays={data?.Reply || []}
          isPublicView={isPublicView}
        />
      )}
      {currentView === "Likes" && <LikesView isPublicView={isPublicView} />}
      {currentView === "Attending" && <AttendingView isPublicView={isPublicView} />}
      {currentView === "deciding" && <DecidingView isPublicView={isPublicView} />}
      {currentView === "notAttending" && <NotAttendingView isPublicView={isPublicView} />}
    </div>
    
    {/* Delete Confirmation Modal */}
    <DeleteConformationPopup
      isOpen={showDeleteConfirmation}
      title="Are you sure you want to delete this event?"
      subTitle=""
      onConfirm={() => {
        handleDeleteEvent();
        setShowDeleteConfirmation(false);
      }}
      onCancel={() => setShowDeleteConfirmation(false)}
    />
  </div>
);
}

const CommetsView = ({
  eventId,
  comments,
  replays,
  isPublicView = false,
}: {
  eventId: string;
  comments: CommentModelT[];
  replays: SubCommentModelT[];
  isPublicView?: boolean;
}) => {
  const { userId } = useAuth();

  const { data: AccountData } = useGetAccounDetails({
    userId: userId,
  });

  const { mutate: createComment } = useCreateComment();
  const { mutate: deleteComment } = useDeleteComment();
  const { mutate: reportComment } = useReportComment();
  const { mutate: createSubComment } = useCreateSubComment();
  const { mutate: reportSubComment } = useReportSubComment();
  const { mutate: deleteSubComment } = useDeleteSubComment();

  const [subComment, setSubComment] = useState("");
  const [subCommentId, setSubCommentId] = useState("");
  const [comment, setComment] = useState("");

  const handleCreateComment = () => {
    if (comment.trim() === "") return toast.info("Comment is required");
    if (userId === null) return toast.info("Please login to comment");

    const data: CreateCommentFormT = {
      agendaId: eventId,
      comment: comment,
      usrId: userId || "",
      displayName: AccountData?.accData?.[0]?.businessName || "",
      picture: AccountData?.accData?.[0]?.picture || null,
      commentId: generateUUID("comment"),
      Itemstart: new Date().toISOString(),
    };

    createComment(data, {
      onSuccess() {
        toast.success("Comment created successfully");
        setComment("");
      },
      onError(error) {
        toast.error(error.message);
      },
    });
  };

  const [showCommentDeleteConfirmation, setShowCommentDeleteConfirmation] = useState(false);
  const [commentToDeleteId, setCommentToDeleteId] = useState<string | null>(null);
  const [showSubCommentDeleteConfirmation, setShowSubCommentDeleteConfirmation] = useState(false);
  const [subCommentToDeleteId, setSubCommentToDeleteId] = useState<string | null>(null);

  const handleDeleteComment = (commentId: string) => {
    if (userId === null) return toast.info("Please login to comment");
    setCommentToDeleteId(commentId);
    setShowCommentDeleteConfirmation(true);
  };

  const confirmDeleteComment = () => {
    if (commentToDeleteId === null) return;
    deleteComment(
      { agendaId: eventId, commentId: commentToDeleteId },
      {
        onSuccess() {
          toast.success("Comment deleted successfully");
          setShowCommentDeleteConfirmation(false);
          setCommentToDeleteId(null);
        },
        onError(error: Error) {
          toast.error(error.message);
          setShowCommentDeleteConfirmation(false);
          setCommentToDeleteId(null);
        },
      }
    );
  };

  const handleDeleteSubComment = (replyId: string) => {
    if (userId === null) return toast.info("Please login to comment");
    setSubCommentToDeleteId(replyId);
    setShowSubCommentDeleteConfirmation(true);
  };

  const confirmDeleteSubComment = () => {
    if (subCommentToDeleteId === null) return;
    deleteSubComment(
      { agendaId: eventId, replyId: subCommentToDeleteId },
      {
        onSuccess() {
          toast.success("Subcomment deleted successfully");
          setShowSubCommentDeleteConfirmation(false);
          setSubCommentToDeleteId(null);
        },
        onError(error: Error) {
          toast.error(error.message);
          setShowSubCommentDeleteConfirmation(false);
          setSubCommentToDeleteId(null);
        },
      }
    );
  };

  const handleReportComment = (commentId: string) => {
    if (userId === null) return toast.info("Please login to comment");

    const commentToReport = comments.find((c) => c.commentId === commentId);

    if (commentToReport?.userId === userId) {
      return toast.error("You cannot report your own comment.");
    }

    reportComment(
      {
        agendaId: eventId,
        commentId: commentId,
        usrId: userId || "",
        reason: "Inappropriate content",
      },
      {
        onSuccess() {
          toast.success("Comment reported successfully");
          setComment("");
        },
        onError(error: Error) {
          toast.error(error.message);
        },
      }
    );
  };

  const handleCreateSubComment = (commentId: string) => {
    if (subComment.trim() === "") return toast.info("Sub Comment is required");
    if (userId === null) return toast.info("Please login to comment");

    const data: CreateSubCommentFormT = {
      agendaId: eventId,
      replyId: generateUUID("reply"),
      reply: subComment,
      usrId: userId || "",
      displayName: AccountData?.accData?.[0]?.businessName || "",
      picture: AccountData?.accData?.[0]?.picture || null,
      commentId: commentId,
      comment: "",
      Itemstart: new Date().toISOString(),
    };

    createSubComment(data, {
      onSuccess() {
        toast.success("Sub Comment created successfully");
        setSubComment("");
        setSubCommentId("");
      },
      onError(error) {
        toast.error(error.message);
      },
    });
  };

  const handleReportSubComment = (commentId: string) => {
    if (userId === null) return toast.info("Please login to comment");

    const subCommentToReport = replays.find((r) => r.replyId === commentId);
    if (subCommentToReport?.userId === userId) {
      return toast.error("You cannot report your own comment.");
    }

    reportSubComment(
      {
        agendaId: eventId,
        replyId: commentId,
        usrId: userId || "",
        reason: "Inappropriate content",
      },
      {
        onSuccess() {
          toast.success("Sub Comment reported successfully");
        },
        onError(error: Error) {
          toast.error(error.message);
        },
      }
    );
  };

  const getSubComment = (commentId: string) => {
    return replays.filter((row) => row.commentId === commentId);
  };

  return (
    <div className="w-full rounded-xl flex flex-col p-3 mx-auto bg-fb-bPrime-50 relative">
      <p className="text-base font-semibold">Comments</p>
      
      {/* Comments content */}
      <div className={cn(
        "flex flex-col gap-2 px-3 py-10",
        isPublicView && "blur-sm pointer-events-none"
      )}>
        {comments.map((row, ind) => {
          return (
            <div className="flex flex-col gap-1" key={ind}>
              <div className="flex items-center gap-2 border-b border-black/60 pb-1.5">
                <img
                  src={row.picture && row.picture.length > 0 ? row.picture : NoImagePlaceholder}
                  alt=""
                  className="h-8 w-8 overflow-hidden rounded-full object-cover"
                />
                <div className="text-xs leading-4">
                  <p className="text-fb-neutral-900">{row.displayName}</p>
                  <p className="text-fb-neutral-800">{row.comment}</p>
                </div>
                {!isPublicView && (
                  <div className="flex gap-2 ml-auto items-center">
                    <button onClick={() => setSubCommentId(row.commentId)}>
                      <img src={MessageIcon} alt="" className="w-3" />
                    </button>
                    <Heart className="size-3" />
                    {row.userId === userId && (
                      <button onClick={() => handleDeleteComment(row.commentId)}>
                        <Trash2 className="size-3 text-red-500" />
                      </button>
                    )}
                    {row.userId !== userId && (
                      <button onClick={() => handleReportComment(row.commentId)}>
                        <Flag
                          className={cn(
                            "size-3",
                            row.report && "fill-fb-warn-600"
                          )}
                        />
                      </button>
                    )}
                  </div>
                )}
              </div>
              
              {/* Sub-comments and input - only show for non-public view */}
              {!isPublicView && subCommentId === row.commentId && (
                <div className="flex flex-col gap-1 relative">
                  <Input
                    value={subComment}
                    onChange={(e) => setSubComment(e.target.value)}
                    onKeyDown={(e) =>
                      e.key === "Enter" && handleCreateSubComment(row.commentId)
                    }
                    placeholder="Type your comment here"
                    className="bg-[#64769330] h-7 border-none text-black rounded-lg !text-xs"
                  />
                  <Button
                    variant={"ghost"}
                    size={"icon"}
                    className="h-5 w-5 absolute top-1 right-2"
                    onClick={() => handleCreateSubComment(row.commentId)}
                  >
                    <SendHorizonal className="size-4" />
                  </Button>
                </div>
              )}
              
              {getSubComment(row.commentId).map((row2, in2) => {
                return (
                  <div
                    key={in2}
                    className="flex items-center gap-2 ml-6 border-b border-black/60 pb-1.5"
                  >
                    <img
                      src={row2.picture && row2.picture.length > 0 ? row2.picture : NoImagePlaceholder}
                      alt=""
                      className="h-8 w-8 overflow-hidden rounded-full object-cover"
                    />
                    <div className="text-xs leading-4">
                      <p className="text-fb-neutral-900">{row2.displayName}</p>
                      <p className="text-fb-neutral-800">{row2.reply}</p>
                    </div>
                    {!isPublicView && (
                      <div className="flex gap-2 ml-auto items-center">
                        <Heart className="size-3" />
                        {row2.userId === userId && (
                          <button
                            onClick={() => handleDeleteSubComment(row2.replyId)}
                          >
                            <Trash2 className="size-3 text-red-500" />
                          </button>
                        )}
                        {row2.userId !== userId && (
                          <button
                            onClick={() => handleReportSubComment(row2.replyId)}
                          >
                            <Flag
                              className={cn(
                                "size-3",
                                row2.report && "fill-fb-warn-600"
                              )}
                            />
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          );
        })}
      </div>
      
      {/* Comment input - only show for non-public view */}
      {!isPublicView && (
        <div className="w-full relative">
          <Input
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            onKeyDown={(e) => e.key === "Enter" && handleCreateComment()}
            placeholder="Type your question here"
            className="bg-[#64769330] h-7 border-none text-black rounded-lg !text-xs"
          />
          <Button
            variant={"ghost"}
            size={"icon"}
            className="h-5 w-5 absolute top-1 right-2"
            onClick={handleCreateComment}
          >
            <SendHorizonal className="size-4" />
          </Button>
        </div>
      )}
      
      {/* Public view overlay */}
      {isPublicView && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/80 rounded-xl">
          <div className="text-center">
            <p className="text-lg font-semibold text-gray-600">
              Log in to Fragment to explore events, interact with
            </p>
            <p className="text-lg font-semibold text-gray-600">
              organisers, and create your own planning space.
            </p>
          </div>
        </div>
      )}
      
      {/* Delete confirmation modals - only for non-public view */}
      {!isPublicView && (
        <>
          <DeleteConformationPopup
            isOpen={showCommentDeleteConfirmation}
            title="Are you sure you want to delete this comment?"
            subTitle=""
            onConfirm={confirmDeleteComment}
            onCancel={() => setShowCommentDeleteConfirmation(false)}
          />
          <DeleteConformationPopup
            isOpen={showSubCommentDeleteConfirmation}
            title="Are you sure you want to delete this sub-comment?"
            subTitle=""
            onConfirm={confirmDeleteSubComment}
            onCancel={() => setShowSubCommentDeleteConfirmation(false)}
          />
        </>
      )}
    </div>
  );
};

const LikesView = ({ isPublicView = false }: { isPublicView?: boolean }) => {
  return (
    <div className="max-w-lg w-full rounded-xl flex flex-col p-3 mx-auto bg-fb-bPrime-50 relative">
      <p className="text-base font-semibold">People who liked this event</p>
      <div className={cn(
        "flex flex-col gap-2 px-3 py-2",
        isPublicView && "blur-sm pointer-events-none"
      )}>
        {UserDummyEventsLike.map((row, ind) => {
          return <PersonDetails row={row} key={ind} isPublicView={isPublicView} />;
        })}
      </div>
      
      {isPublicView && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/80 rounded-xl">
          <div className="text-center">
            <p className="text-lg font-semibold text-gray-600">
              Log in to Fragment to explore events, interact with
            </p>
            <p className="text-lg font-semibold text-gray-600">
              organisers, and create your own planning space.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

const AttendingView = ({ isPublicView = false }: { isPublicView?: boolean }) => {
  return (
    <div className="max-w-lg w-full rounded-xl flex flex-col p-3 mx-auto bg-fb-bPrime-50 relative">
      <p className="text-base font-semibold">People attending this event</p>
      <div className={cn(
        "flex flex-col gap-2 px-3 py-2",
        isPublicView && "blur-sm pointer-events-none"
      )}>
        {UserDummyEventsLike.map((row, ind) => {
          return <PersonDetails row={row} key={ind} isPublicView={isPublicView} />;
        })}
      </div>
      
      {isPublicView && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/80 rounded-xl">
          <div className="text-center">
            <p className="text-lg font-semibold text-gray-600">
              Log in to Fragment to explore events, interact with
            </p>
            <p className="text-lg font-semibold text-gray-600">
              organisers, and create your own planning space.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

const DecidingView = ({ isPublicView = false }: { isPublicView?: boolean }) => {
  return (
    <div className="max-w-lg w-full rounded-xl flex flex-col p-3 mx-auto bg-fb-bPrime-50 relative">
      <p className="text-base font-semibold">
        People deciding about this event
      </p>
      <div className={cn(
        "flex flex-col gap-2 px-3 py-2",
        isPublicView && "blur-sm pointer-events-none"
      )}>
        {UserDummyEventsLike.map((row, ind) => {
          return <PersonDetails row={row} key={ind} isPublicView={isPublicView} />;
        })}
      </div>
      
      {isPublicView && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/80 rounded-xl">
          <div className="text-center">
            <p className="text-lg font-semibold text-gray-600">
              Log in to Fragment to explore events, interact with
            </p>
            <p className="text-lg font-semibold text-gray-600">
              organisers, and create your own planning space.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

const NotAttendingView = ({ isPublicView = false }: { isPublicView?: boolean }) => {
  return (
    <div className="max-w-lg w-full rounded-xl flex flex-col p-3 mx-auto bg-fb-bPrime-50 relative">
      <p className="text-base font-semibold">
        People who not attending this event
      </p>
      <div className={cn(
        "flex flex-col gap-2 px-3 py-2",
        isPublicView && "blur-sm pointer-events-none"
      )}>
        {UserDummyEventsLike.map((row, ind) => {
          return <PersonDetails row={row} key={ind} isPublicView={isPublicView} />;
        })}
      </div>
      
      {isPublicView && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/80 rounded-xl">
          <div className="text-center">
            <p className="text-lg font-semibold text-gray-600">
              Log in to Fragment to explore events, interact with
            </p>
            <p className="text-lg font-semibold text-gray-600">
              organisers, and create your own planning space.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

const PersonDetails = ({ 
  row, 
  isPublicView = false 
}: { 
  row: { name: string; image: string }; 
  isPublicView?: boolean; 
}) => {
  return (
    <div className="flex items-center gap-2">
      <img
        src={row.image}
        alt=""
        className="h-5 w-5 overflow-hidden rounded-full object-cover"
      />
      <p className="text-sm">{row.name}</p>
      {!isPublicView && (
        <div className="flex gap-2 ml-auto items-center">
          <img src={MessageIcon} alt="" className="w-4" />
          <Flag className="size-3.5" />
        </div>
      )}
    </div>
  );
};
