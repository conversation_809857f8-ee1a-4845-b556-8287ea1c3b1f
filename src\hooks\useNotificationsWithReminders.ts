// src/hooks/useNotificationsWithReminders.ts
import { useState, useEffect } from 'react';
import { reminderScheduler } from '../services/ReminderScheduler';
import { ReminderNotification } from '../types/reminder';

export interface Notification {
  id: string;
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  type?: 'reminder' | 'general';
  eventId?: string;
  avatar?: string;
}

export const useNotificationsWithReminders = () => {
  const [notifications, setNotifications] = useState<Notification[]>(() => {
    try {
      const storedNotifications = localStorage.getItem('inAppNotifications');
      return storedNotifications ? JSON.parse(storedNotifications) : [];
    } catch (error) {
      console.error('Failed to load in-app notifications from storage:', error);
      return [];
    }
  });

  // Effect to save notifications to localStorage whenever they change
  useEffect(() => {
    try {
      localStorage.setItem('inAppNotifications', JSON.stringify(notifications));
    } catch (error) {
      console.error('Failed to save in-app notifications to storage:', error);
    }
  }, [notifications]);

  useEffect(() => {
    console.log('[useNotificationsWithReminders] Hook is running, attempting to subscribe.');
    // Request notification permission
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }

    // Subscribe to reminder notifications
    const unsubscribe = reminderScheduler.onNotification((reminderNotification: ReminderNotification) => {
      setNotifications(prev => [
        {
          ...reminderNotification,
          avatar: '/icons/reminder.png' // Add a reminder icon
        },
        ...prev
      ]);
    });

    return () => {
      unsubscribe();
    };
  }, []);

  const addNotification = (notification: Omit<Notification, 'id'>) => {
    const newNotification = {
      ...notification,
      id: `notification_${Date.now()}_${Math.random()}`
    };
    setNotifications(prev => [newNotification, ...prev]);
  };

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, read: true }
          : notification
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    );
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  const requestNotificationPermission = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      console.log('Notification permission:', permission);
      return permission;
    }
    return 'unsupported';
  };

  return {
    notifications,
    addNotification,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    requestNotificationPermission,
    notificationPermission: Notification.permission
  };
};
