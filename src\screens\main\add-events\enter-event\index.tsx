import { toast } from "sonner";
import { FormEvent, useState } from "react";
import { Link, useLocation as useRouterLocation, useNavigate } from "react-router-dom";

import { FileWithPreview } from "@/types";
import { Input } from "@/components/ui/input";
import { Image } from "@/components/ui/image";
import { Button } from "@/components/ui/button";
import AchiveIcon from "@/assets/svg/achive-icon.svg";
import PlaceholderImg from "@/assets/images/add-placeholder.jpg";
import BussinessInfo from "@/components/common/bussiness-info";
import { cn } from "@/lib/utils";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { DateTimePicker } from "@/components/common/date-time-picker";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { X } from "lucide-react";
// import InfoIcon from "@/assets/svg/Info-circle-icon.svg";
import { PdfIcon } from "@/assets/svg/icons";
import { useEventWithReminders } from "@/hooks/useEventWithReminders";
import { useNotificationsWithReminders } from "@/hooks/useNotificationsWithReminders";
import {
  currentTimeZone,
  generateUUID,
  getPreviousHalfHour,
  getZoneFromTzCode,
} from "@/utils";
import { CreatEventFormT } from "@/types/api";
import { rawEventFormToModel } from "@/utils/model";
import { ConformationPopup } from "@/components/common/conformation-popup";
import { useCalendarsWithEvents } from "@/hook/useCalendarsWithEvents";
import { useAuth } from "@/context/AuthContext";
import { addHours, addMonths } from "date-fns";
import { useCustomDropzone, FILE_TYPES } from "@/hook/useCustomDropzone";

const ReminderEventTime = [
  { name: "5 Mins", value: "5-min" },
  { name: "10 Mins", value: "10-min" },
  { name: "15 Mins", value: "15-min" },
  { name: "30 Mins", value: "30-min" },
  { name: "1 Hour", value: "1-hour" },
  { name: "1 Day", value: "1-day" },
];

const RepeatEventTime = [
  { name: "Never", value: "Never" },
  { name: "Daily", value: "Daily" },
  { name: "Weekly", value: "Weekly" },
  { name: "Monthly", value: "Monthly" },
];

const RepeatEveryTime = Array.from({ length: 30 }, (_, i) => ({
  name: `${i + 1}`,
  value: `${i + 1}`,
}));

export default function AddEventWithEnterScreen() {
  const { createEventWithReminder, isCreating: isPending } = useEventWithReminders();
  const navigaiton = useNavigate();
  const routerLocation = useRouterLocation();
  const queryParams = new URLSearchParams(routerLocation.search);
  const taskName = queryParams.get("name");

  const { userId } = useAuth();

  const { apiCalendars } = useCalendarsWithEvents(userId);
  const { requestNotificationPermission } = useNotificationsWithReminders();

  const [eventImage, setEventImage] = useState<FileWithPreview[]>([]);

  const [eventName, setEventName] = useState(taskName || "");
  const [isAllDayEvent, setIsAllDayEvent] = useState(false);
  const [chooseCalendar, setChooseCalendar] = useState<string[]>([]);
  const [reminderTime, setReminderTime] = useState<string | null>("15-min");
  const [isReminderActive, setIsReminderActive] = useState(true);
  const [repeatTime, setRepeatTime] = useState<string | null>("Never");
  const [repeatEndDate, setRepeatEndDate] = useState<Date | null>(null);
  const [repeatEveryValue, setRepeatEveryValue] = useState<string>("1");

  const [attachedLinks, setAttachedLinks] = useState<string[]>([""]);

  const [startDate, setStartDate] = useState<Date>(() => getPreviousHalfHour());
  const [endDate, setEndDate] = useState<Date>(() => {
    const start = getPreviousHalfHour();
    start.setHours(start.getHours() + 1);
    return start;
  });

  const [location, setLocation] = useState("");
  const [description, setDescription] = useState("");

  const [conformationPopup, setConformationPopup] = useState({
    open: false,
    title: "",
    subTitle: "",
  });

  const handleLinkChange = (index: number, value: string) => {
    const newLinks = [...attachedLinks];
    newLinks[index] = value;
    setAttachedLinks(newLinks);
  };

  const handleLinkRemove = (index: number) => {
    const updated = attachedLinks.filter((_, i) => i !== index);
    setAttachedLinks(updated);
  };

  const handleAddLink = () => {
    setAttachedLinks([...attachedLinks, ""]);
  };

  const { getRootProps, getInputProps } = useCustomDropzone({
    acceptedFileTypes: FILE_TYPES.IMAGES,
    maxFiles: 1,
    maxSize: 1 * 1024 * 1024, // 1MB
    multiple: false,
    showToastOnError: true,
    customErrorMessages: {
      "file-invalid-type": "Please upload only image files for event cover",
      "file-too-large": "Event image is too large",
      "too-many-files": "You can only upload one event image",
    },
    onFilesAccepted: (acceptedFiles) => {
      setEventImage(
        acceptedFiles.map((file) =>
          Object.assign(file, {
            preview: URL.createObjectURL(file),
          })
        )
      );
    },
    // No onFilesRejected - errors handled automatically
  });

  

  const handleCreateEvent = async (e: FormEvent) => {
    e.preventDefault();

    if (!chooseCalendar.length || !apiCalendars) {
      toast.info("At least one calendar is required");
      return;
    }

    const processedLinks = attachedLinks
      .map((link) => {
        const trimmedLink = link.trim();
        if (trimmedLink && !trimmedLink.startsWith("http://") && !trimmedLink.startsWith("https://")) {
          return `https://${trimmedLink}`;
        }
        return trimmedLink;
      })
      .filter((link) => link);

    let successful = 0;
    let failed = 0;

    for (const calendarId of chooseCalendar) {
      const calendar = apiCalendars.find((cal) => cal._id === calendarId);
      if (!calendar) {
        console.error(`Calendar with ID ${calendarId} not found. Skipping event creation for this calendar.`);
        failed++;
        continue;
      }

      try {
        const FormData: CreatEventFormT = {
          _id: generateUUID("bizAgenda"),
          calId: calendarId,
          calendarName: calendar.calendarName || "", // Ensure calendarName is used from the found calendar
          end: endDate,
          start: startDate,
          event: true,
          note: false,
          title: eventName,
          userType: "businessUser",
          usrId: userId || "",
          zone: getZoneFromTzCode(currentTimeZone),
          allDayEvent: isAllDayEvent,
          blocked: false,
          deleted: false,
          location,
          description,
          reminder: reminderTime,
          reminderSelected: !!reminderTime,
          frequency: repeatTime === "Never" ? undefined : repeatTime,
          days: [], // Assuming no specific day selection for now
          repeat: repeatTime !== "Never",
          repeatEvery: repeatTime === "Never" ? undefined : parseInt(repeatEveryValue),
          repeatEndDate:
            repeatTime === "Never" || repeatEndDate === null
              ? undefined
              : repeatEndDate,
          link: processedLinks,
          profileImage: eventImage[0] || null,
          picture: undefined,
        };

        const NewData = rawEventFormToModel(FormData);
        await createEventWithReminder({
          ...NewData,
          dateTime: startDate.toISOString(), // Ensure date is ISO string
          reminderSelected: isReminderActive,
          reminder: isReminderActive ? reminderTime : null,
        });
        successful++;
      } catch (error) {
        console.error(`Failed to create event in calendar ${calendar.calendarName} (ID: ${calendarId}):`, error);
        failed++;
      }
    }

    if (successful > 0) {
      handleSetConformation({
        title: `Event created in ${successful} calendar${
          successful > 1 ? "s" : ""
        }${failed > 0 ? `, ${failed} failed` : ""}`,
        subTitle: "",
      });
    } else {
      toast.error("Failed to create event in any calendar");
    }
  };

  const handleSetConformation = ({
    title,
    subTitle,
  }: {
    title: string;
    subTitle: string;
  }) => {
    setConformationPopup({ open: true, title: title, subTitle: subTitle });
  };

  const handleClosePopups = () => {
    setConformationPopup({ open: false, title: "", subTitle: "" });
    navigaiton("/");
    //  setAlertPopup({ open: false, title: "" });
  };

  return (
    <div className="flex flex-col w-full h-dvh pr-3">
      <div className="justify-end flex items-end">
        <div className="w-52 lg:w-64">
          <BussinessInfo />
        </div>
      </div>
      <form
        onSubmit={handleCreateEvent}
        className="flex w-full h-[calc(100dvh-40px)] lg:h-[calc(100dvh-48px)] py-3 overflow-y-auto gap-3">
        {/* side bar */}
        <div className="w-80 lg:w-96 min-w-80 lg:min-w-96 flex flex-col p-6 gap-3 overflow-y-auto no-scrollbar bg-white rounded-2xl">
          <div className="px-2  flex items-center justify-between">
            <p className="text-black font-bold text-xl">New Event</p>
            <button
              type="button"
              onClick={() => {
                navigaiton(-1);
              }}>
              <img src={AchiveIcon} className="size-5" />
            </button>
          </div>
          <div className="flex flex-col w-full">
            {eventImage.length > 0 ? (
              <div className="w-full h-64 rounded-12px overflow-hidden relative">
                <Image
                  src={eventImage[0].preview}
                  fallbackSrc={PlaceholderImg}
                />
                <Button
                  type="button"
                  variant={"ghost"}
                  size={"icon"}
                  className="absolute top-2 right-2 h-6 w-6"
                  onClick={() => {
                    setEventImage([]);
                  }}>
                  <X className="text-fb-warn-600" />
                </Button>
              </div>
            ) : (
              <div
                {...getRootProps({ className: "dropzone" })}
                className="bg-fb-neutral-100 rounded-12px h-64 flex justify-center items-center cursor-pointer border-2 border-dashed border-fb-neutral-400 relative overflow-hidden">
                <input {...getInputProps()} />
                <Image
                  src={PlaceholderImg}
                  className="absolute inset-0 w-full h-full object-cover opacity-50"
                />
                <div className="text-center relative z-10">
                  <p className="text-lg font-semibold text-fb-neutral-700">
                    Add an image here
                  </p>
                  <p className="text-sm text-fb-neutral-500">
                    Click or drag 'n' drop
                  </p>
                </div>
              </div>
            )}
          </div>
          <div className="flex flex-col gap-1">
            <Label htmlFor="EventName" className="text-fb-neutral-400 pl-3">
              Event Name
            </Label>

            <Input
              required
              id="EventName"
              value={eventName}
              onChange={(e) => {
                setEventName(e.target.value);
              }}
              className="!rounded-12px border-fb-neutral-400"
              placeholder="Name"
            />
          </div>
          <div className="flex flex-col gap-1">
            <Label className="text-fb-neutral-400 pl-3">Choose Calendar(s)</Label>
            <div className="grid grid-cols-3 gap-x-2 gap-y-1 mb-1">
              {apiCalendars?.slice(0, 3).map((cal, ind) => (
                <div
                  onClick={() => {
                    setChooseCalendar(prev => {
                      if (prev.includes(cal._id)) {
                        return prev.filter(id => id !== cal._id);
                      } else {
                        return [...prev, cal._id];
                      }
                    });
                  }}
                  key={ind}
                  className={cn(
                    "border rounded-8px cursor-pointer h-6 px-1 gap-1 flex items-center",
                    chooseCalendar.includes(cal._id)
                      ? "bg-fb-bPrime-100 border-fb-neutral-900 text-fb-neutral-800"
                      : "bg-fb-neutral-50 border-fb-neutral-500 text-fb-neutral-600"
                  )}>
                  <div className={cn(
                    "w-3 h-3 min-w-3 rounded-full",
                    chooseCalendar.includes(cal._id) 
                      ? "bg-fb-bPrime-500" 
                      : "bg-fb-neutral-400"
                  )} />
                  <p className="truncate text-xs font-semibold">
                    {cal.calendarName}
                  </p>
                </div>
              ))}
            </div>
          </div>
       
          <Accordion type="single" collapsible>
            <AccordionItem value="item-1">
              
              <AccordionContent>
                {apiCalendars && apiCalendars.length > 3 && (
                  <div className="grid grid-cols-3 gap-x-2 gap-y-0">
                    {apiCalendars.slice(3).map((cal, ind) => (
                      <div
                        onClick={() => {
                          setChooseCalendar(prev => {
                            if (prev.includes(cal._id)) {
                              return prev.filter(id => id !== cal._id);
                            } else {
                              return [...prev, cal._id];
                            }
                          });
                        }}
                        key={ind}
                        className={cn(
                          "border rounded-8px cursor-pointer h-6 px-1 gap-1 flex items-center",
                          chooseCalendar.includes(cal._id)
                            ? "bg-fb-bPrime-100 border-fb-neutral-900 text-fb-neutral-800"
                            : "bg-fb-neutral-50 border-fb-neutral-500 text-fb-neutral-600"
                        )}>
                        <div className={cn(
                          "w-3 h-3 min-w-3 rounded-full",
                          chooseCalendar.includes(cal._id) 
                            ? "bg-fb-bPrime-500" 
                            : "bg-fb-neutral-400"
                        )} />
                        <p className="truncate text-xs font-semibold">
                          {cal.calendarName}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </AccordionContent>
              <AccordionTrigger className="justify-end gap-2 text-sm text-fb-neutral-400 p-0">
                More
              </AccordionTrigger>
            </AccordionItem>
          </Accordion>
             <div className="flex items-center space-x-2">
            <Label htmlFor="all-day">All day</Label>
            <Switch
              checked={isAllDayEvent}
              onCheckedChange={setIsAllDayEvent}
              id="all-day"
              size="md"
            />
          </div>
          <div className="w-full flex flex-col gap-0.5 items-center justify-center mb-2">
            <div className="flex gap-1 items-center">
              <DateTimePicker
                date={startDate}
                setDate={(date) => {
                  setStartDate(date);
                  setEndDate(addHours(date, 1));
                }}
                isSmallView
                showDay={false}
              />
              <div className="w-12 h-0.5 rounded-full bg-black/70" />
              <DateTimePicker
                date={endDate}
                setDate={setEndDate}
                isSmallView
                showDay={false}
              />
            </div>
          </div>
          <div className="flex items-center justify-start gap-4">
            <div className="flex items-center gap-2">
                <Switch
                    id="reminder-switch"
                    checked={isReminderActive}
                    onCheckedChange={async (checked) => {
                        setIsReminderActive(checked);
                        if (checked) {
                            // Request permission when reminder is activated
                            const permission = await requestNotificationPermission();
                            if (permission === 'granted') {
                                setReminderTime("15-min");
                            } else {
                                // If permission not granted, revert switch and show message
                                setIsReminderActive(false);
                                toast.error("Notification permission denied. Please enable it in your browser settings to receive reminders.");
                            }
                        } else {
                            setReminderTime(null);
                        }
                    }}
                />
                <Label htmlFor="reminder-switch" className="text-fb-neutral-400 font-bold">Reminder</Label>
            </div>
            {isReminderActive && (
                <Select
                onValueChange={setReminderTime}
                value={reminderTime || ""}>
                <SelectTrigger className="!rounded-12px border-fb-neutral-400 w-fit h-8">
                    <SelectValue placeholder="Select reminder time" />
                </SelectTrigger>
                <SelectContent>
                    {ReminderEventTime.map((remind) => (
                    <SelectItem key={remind.value} value={remind.value}>
                        {remind.name}
                    </SelectItem>
                    ))}
                </SelectContent>
                </Select>
            )}
          </div>
        </div>
        {/*  views */}
        <div className="flex flex-col flex-1 gap-2 ">
          <div className="border border-fb-neutral-40 rounded-12px p-2 flex gap-4 justify-around items-center">
            <p className="text-fb-neutral-700 text-lg ">OR</p>
            <p className="text-fb-neutral-700">Add multiple events at a time</p>
            <Link to={"/add-event-upload"}>
              <Button
                type="button"
                className="bg-fb-bPrime-500 h-8 w-96 text-white">
                <PdfIcon className="text-white size-6" />
                Upload Events from File
              </Button>
            </Link>
          </div>
          <div className="flex flex-col gap-2 p-6 w-full h-full bg-white rounded-2xl overflow-y-auto no-scrollbar">
            <div className="flex justify-end gap-1 ">
              <Button
                onClick={() => {
                  navigaiton(-1);
                }}
                size={"sm"}
                variant={"ghost"}
                type="button"
                className="rounded-full h-7 text-fb-warn-500">
                Cancel
                <X className="size-4 text-black" />
              </Button>
            </div>
            <Separator />
            <div className="grid grid-cols-7">
              <div className="col-span-2 ">
                <p className="text-fb-neutral-600 font-semibold text-lg">
                  Description :
                </p>
              </div>
              <div className="col-span-5 gap-1">
                <Textarea
                  value={description}
                  onChange={(e) => {
                    setDescription(e.target.value);
                  }}
                  className="!rounded-12px border-fb-neutral-400 resize-none"
                  placeholder="Description"
                />
              </div>
            </div>
            <Separator />
            <div className="grid grid-cols-7 items-center">
              <div className="col-span-2 ">
                <p className="text-fb-neutral-600 font-semibold text-lg">
                  Repeat this event :
                </p>
              </div>
              <div className="col-span-5 grid grid-cols-4 gap-1">
                {RepeatEventTime.map((remind, ind) => {
                  return (
                    <div
                      className={cn(
                        "h-9 rounded-12px border cursor-pointer flex items-center justify-center text-sm",
                        repeatTime === remind.value
                          ? "text-fb-neutral-700 border-fb-neutral-600 bg-fb-neutral-100"
                          : "text-fb-neutral-600 border-fb-neutral-400 bg-white"
                      )}
                      key={ind}
                      onClick={() => {
                        setRepeatTime(remind.value);
                        // Set repeat end date to 3 months from current date when repeat is selected
                        if (remind.value !== "Never") {
                          setRepeatEndDate(addMonths(new Date(), 3));
                        } else {
                          setRepeatEndDate(null);
                        }
                      }}>
                      {remind.name}
                    </div>
                  );
                })}
              </div>
            </div>
            <Separator />
            {repeatTime !== "Never" && repeatTime !== null && (
              <>
                <div className="grid grid-cols-7 items-center">
                  <div className="col-span-2 ">
                    <p className="text-fb-neutral-600 font-semibold text-lg">
                      Repeat Every :
                    </p>
                  </div>
                  <div className="col-span-5">
                    <Select
                      onValueChange={setRepeatEveryValue}
                      value={repeatEveryValue}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select a number" />
                      </SelectTrigger>
                      <SelectContent>
                        {RepeatEveryTime.map((item) => (
                          <SelectItem key={item.value} value={item.value}>
                            {item.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-7 items-center">
                  <div className="col-span-2 ">
                    <p className="text-fb-neutral-600 font-semibold text-lg">
                      Repeat End Date :
                    </p>
                  </div>
                  <div className="col-span-5">
                    <DateTimePicker
                      date={repeatEndDate || undefined}
                      setDate={setRepeatEndDate}
                      isSmallView
                      showDay={true}
                      disableTimeSelection={true}
                    />
                  </div>
                </div>
              </>
            )}
            <div className="grid grid-cols-7">
              <div className="col-span-2 ">
                <p className="text-fb-neutral-600 font-semibold text-lg">
                  Location :
                </p>
              </div>
              <div className="col-span-5 gap-2 flex flex-col">
                <div className="flex items-center relative">
                  <Input
                    value={location}
                    onChange={(e) => setLocation(e.target.value)}
                    className="!rounded-12px border-fb-neutral-400"
                    placeholder="Location"
                  />
                </div>
                {/*
                <div className="overflow-hidden rounded-12px h-28 w-full border border-fb-neutral-400">
                  <Image
                    src={LocationImage}
                    className="contrast-75 opacity-30"
                  />
                </div>
                */}
              </div>
            </div>
            <Separator />
            <div className="grid grid-cols-7">
              <div className="col-span-2 ">
                <p className="text-fb-neutral-600 font-semibold text-lg">
                  Add Links :
                </p>
              </div>
              <div className="col-span-5 gap-2 flex flex-col">
                {attachedLinks.map((links, ind) => {
                  return (
                    <div className="relative flex items-center" key={ind}>
                      <Input
                        key={ind}
                        value={links}
                        type="text"
                        className="!rounded-12px border-fb-neutral-400 pr-8"
                        placeholder="Enter Link"
                        onChange={(e) => {
                          handleLinkChange(ind, e.target.value);
                        }}
                      />
                      <Button
                        type="button"
                        variant={"ghost"}
                        size={"icon"}
                        className="absolute right-1 !h-7 !w-7 rounded-full"
                        onClick={() => handleLinkRemove(ind)}>
                        <X />
                      </Button>
                    </div>
                  );
                })}
                <Button
                  type="button"
                  variant={"outline"}
                  className="!rounded-12px border-fb-neutral-400 text-fb-neutral-600"
                  onClick={handleAddLink}>
                  Add a New Link
                </Button>
              </div>
            </div>
            
            <Separator />
            {/* <div className="grid grid-cols-7">
              <div className="col-span-2 ">
                <p className="text-fb-neutral-600 font-semibold text-lg">
                  User Interactions :
                </p>
              </div>
              <div className="col-span-5 gap-2 flex flex-col">
                <div className=" flex items-center gap-3 justify-between">
                  <Label
                    htmlFor="option-RSVP"
                    className="text-sm lg:text-base -tracking-wider font-normal flex gap-0.5 text-center cursor-pointer text-fb-neutral-600">
                    Provide RSVP
                    <img src={InfoIcon} alt="" className="w-4 h-4" />
                  </Label>
                  <Switch id="option-RSVP" size="md" />
                </div>
                <div className=" flex items-center gap-3 justify-between">
                  <Label
                    htmlFor="allow-comments"
                    className="text-sm lg:text-base -tracking-wider font-normal flex gap-0.5 text-center cursor-pointer text-fb-neutral-600">
                    Allow Comments
                    <img src={InfoIcon} alt="" className="w-4 h-4" />
                  </Label>
                  <Switch id="allow-comments" size="md" />
                </div>
                <div className=" flex items-center gap-3 justify-between">
                  <Label
                    htmlFor="RSVP-comments"
                    className="text-sm lg:text-base -tracking-wider font-normal flex gap-0.5 cursor-pointer text-fb-neutral-600">
                    Allow Users see likes
                    <img src={InfoIcon} alt="" className="w-4 h-4" />
                  </Label>
                  <Switch id="RSVP-comments" size="md" />
                </div>
              </div>
            </div> */}
            {/* <Separator /> */}
            <div className="flex justify-end">
              <Button
                type="submit"
                disabled={isPending}
                className="bg-fb-bPrime-600 !rounded-12px w-80">
                Add Event
              </Button>
            </div>
          </div>
        </div>
      </form>

      <ConformationPopup
        isOpen={conformationPopup.open}
        title={conformationPopup.title}
        subTitle={conformationPopup.subTitle}
        onCLose={handleClosePopups}
      />
    </div>
  );
}
