@tailwind base;
@tailwind components;
@tailwind utilities;


@font-face {
  font-family: "sf-pro";
  src: url("./assets/fonts/SF-Pro.ttf") format("opentype");
}

:root {
  font-family: sf-pro, sans-serif;
  line-height: 1.2;
  font-weight: 400;
  /* padding: 8px; */

  color-scheme: light dark;
  color: #000;
  background-color: #ecedf0;
}

/* Common Style overrides for react-big-calendar */
.rbc-month-view,
.rbc-time-view {
  border: none !important;
  padding: 4px;
  background-color: #fff;
  border-radius: 16px;
}

.rbc-month-header,
.rbc-month-row,
.rbc-header {
  border: none !important;
}

.rbc-row-bg {
  gap: 4px;
}

.rbc-overlay {
  border-radius: 12px;
  background-color: red;
  padding: 10px 6px !important;
}

.rbc-show-more {
  padding: 0 0.5rem !important;
  border-radius: 16px;
  background-color: transparent !important;
}

/* Hiding the time header and gutter as we are not using it in week view */
.rbc-time-header-gutter,
.rbc-time-gutter {
  display: none !important;
}

/* ------------------------------------------------------------------ */
/* Styling specific to Week View Begins */

/* Removing the min-height of week view's header, if all day is needed, update this */
.rbc-time-view .rbc-row {
  min-height: auto !important;
}

.rbc-header,
rbc-today {
  text-align: end !important;
  font-size: 100% !important;
  padding: 0 !important;
}

.rbc-time-content,
.rbc-events-container,
.rbc-timeslot-group,
.rbc-time-header-content,
.rbc-time-header.rbc-overflowing {
  border: 0 !important;
}

.rbc-time-header-cell>.rbc-header {
  border-radius: 12px 12px 0 0;
}

.rbc-time-header-cell.rbc-row,
.rbc-time-content {
  gap: 8px;
}

.rbc-time-header-cell>.rbc-header,
.rbc-time-column {
  box-shadow: 2px 2px 8px 2px rgba(0, 0, 0, 0.1);
}

.rbc-time-header {
  margin-right: 1rem;
}

.rbc-day-slot .rbc-events-container {
  margin: 0 4px !important;
}

/* Styling specific to Week View Ends */
/* ------------------------------------------------------------------ */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 224 71.4% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;
    --primary: 220.9 39.3% 11%;
    --primary-foreground: 210 20% 98%;
    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 224 71.4% 4.1%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 210 20% 98%;
    --primary-foreground: 220.9 39.3% 11%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 216 12.2% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

/* Hide scrollbar for Chrome, Safari and Opera */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.no-scrollbar {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

/* AI Chat Welcome Message Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeOutDown {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
}

/* Optional: Add utility classes for easier use */
.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out;
}

.animate-fade-out-down {
  animation: fadeOutDown 0.5s ease-in;
}

/* Custom Scrollbar Styles for AI Chat Component Only */

/* AI Chat Container Scrollbar - Webkit browsers (Chrome, Safari, Edge) */
.ai-chat-container ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.ai-chat-container ::-webkit-scrollbar-track {
  background: rgba(6, 79, 95, 0.3); /* Dark teal background similar to chat bubbles */
  border-radius: 10px;
  border: 1px solid rgba(0, 200, 255, 0.2); /* Subtle cyan border */
}

.ai-chat-container ::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #00c8ff 0%, #0891b2 100%); /* Cyan gradient */
  border-radius: 10px;
  border: 1px solid rgba(0, 200, 255, 0.3);
  transition: all 0.3s ease;
}

.ai-chat-container ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #22d3ee 0%, #0891b2 100%); /* Lighter cyan on hover */
  box-shadow: 0 0 8px rgba(0, 200, 255, 0.5); /* Glowing effect */
}

.ai-chat-container ::-webkit-scrollbar-thumb:active {
  background: linear-gradient(180deg, #0891b2 0%, #164e63 100%); /* Darker when pressed */
}

.ai-chat-container ::-webkit-scrollbar-corner {
  background: rgba(6, 79, 95, 0.3);
}

/* AI Chat Container Scrollbar - Firefox */
.ai-chat-container * {
  scrollbar-width: thin;
  scrollbar-color: #00c8ff rgba(6, 79, 95, 0.3);
}

/* Modal Animations - Share Calendar Popup */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideDown {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.2s ease-out forwards;
}

.animate-slideUp {
  animation: slideUp 0.3s ease-out forwards;
}

.animate-fadeOut {
  animation: fadeOut 0.2s ease-out forwards;
}

.animate-slideDown {
  animation: slideDown 0.3s ease-out forwards;
}
