import { useState } from "react";
import { ChevronLeft, Flag, Users, Copy, X } from "lucide-react";
import { useNavigate, useParams } from "react-router";

import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import EditIcon from "@/assets/svg/edit-icon.svg";
import MessageIcon from "@/assets/svg/message-icon.svg";
import YearCalendar from "@/components/calendar/year-calendar";
import BussinessInfo from "@/components/common/bussiness-info";
import MonthCalendar from "@/components/calendar/month-calendar";
import WeeklyCalendar from "@/components/calendar/weekly-calendar";
import whatsapp from "@/assets/svg/whatsapp.svg";
import facebook from "@/assets/images/facebook.png";
import Xs from "@/assets/svg/X.svg";
import linkedin from "@/assets/svg/linkedin.svg";
import instagram from "@/assets/svg/instagram.svg";
import mail from "@/assets/svg/Mail.svg";
import { toast } from "sonner";

import CalendarTabSwitcher, {
  AvailableCalendarTabs,
} from "@/components/calendar/calendar-tab-switch";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import EventsOnDateView from "@/components/common/event/events-onDate";
import { useCalendarDetailsWithEvents } from "@/hook/useCalenderDetailsWithEvents";
import { Image } from "@/components/ui/image";
import UpComingEvents from "@/components/calendar/upcoming-events";
import { useAuth } from "@/context/AuthContext";
import { useUpdateCalendar } from "@/api/calendar";
import { WEBSITE_URL } from "@/envs";


// Share Calendar Modal Component
const ShareCalendarModal = ({ 
  isOpen, 
  onClose, 
  calendarName, 
  shareUrl 
}: {
  isOpen: boolean;
  onClose: () => void;
  calendarName: string;
  shareUrl: string;
}) => {
  const [, setCopied] = useState(false);
  const [isClosing, setIsClosing] = useState(false);

  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      setIsClosing(false);
      onClose();
    }, 300);
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      toast.success("Link copied to clipboard!");
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast.error("Failed to copy link");
    }
  };

  const shareOptions = [
    {
      name: "WhatsApp",
      icon: whatsapp,
      bgColor: "bg-green-500",
      textColor: "text-white",
      url: `https://wa.me/?text=Check out this calendar: ${calendarName} - ${shareUrl}`
    },
    {
      name: "Mail",
      icon: mail,
      bgColor: "bg-gray-500",
      textColor: "text-white",
      url: `mailto:?subject=Check out this calendar: ${calendarName}&body=I wanted to share this calendar with you: ${shareUrl}`
    },
    {
      name: "X",
      icon: Xs,
      bgColor: "bg-black",
      textColor: "text-white",
      url: `https://twitter.com/intent/tweet?text=Check out this calendar: ${calendarName}&url=${shareUrl}`
    },
    {
      name: "LinkedIn",
      icon: linkedin,
      bgColor: "bg-blue-600",
      textColor: "text-white",
      url: `https://www.linkedin.com/sharing/share-offsite/?url=${shareUrl}`
    },
    {
      name: "Instagram",
      icon: instagram,
      bgColor: "bg-gradient-to-br from-purple-500 to-pink-500",
      textColor: "text-white",
      url: "#" // Instagram doesn't support direct URL sharing
    },
    {
      name: "Facebook",
      icon: facebook,
      bgColor: "bg-blue-500",
      textColor: "text-white",
      url: `https://www.facebook.com/sharer/sharer.php?u=${shareUrl}`
    }
  ];

  const handleShare = (option: typeof shareOptions[0]) => {
    if (option.name === "Instagram") {
      toast.info("Copy the link and share it on Instagram!");
      handleCopyLink();
      return;
    }
    window.open(option.url, '_blank');
  };

  if (!isOpen) return null;

  return (
    <div className={`fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 ${isClosing ? 'animate-fadeOut' : 'animate-fadeIn'}`}>
      <div className={`bg-white rounded-3xl p-8 max-w-lg w-full mx-4 relative ${isClosing ? 'animate-slideDown' : 'animate-slideUp'}`}>
          <button
            onClick={handleClose}
            className="absolute top-4 right-4 p-1 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="size-5" />
          </button>
          
          <div className="text-center mb-8">
            <h2 className="text-xl font-semibold mb-2">
              Share your calendar — {calendarName}
            </h2>
            <p className="text-gray-600">With your users</p>
          </div>

          <div className="flex justify-center gap-3 mb-8 overflow-x-auto">
            {shareOptions.map((option, index) => (
              <button
                key={index}
                onClick={() => handleShare(option)}
                className="flex flex-col items-center gap-2 p-2 hover:bg-gray-50 rounded-lg transition-colors min-w-[60px]"
              >
                <div className={`w-10 h-10 ${option.textColor} hover:scale-105`}>
                  <img 
                    src={option.icon} 
                    alt={`${option.name} icon`} 
                    className="w-10 h-10 object-contain"
                  />
                </div>
                <span className="text-xs text-gray-600 text-center">{option.name}</span>
              </button>
            ))}
          </div>

          <div className="mb-8">
            <div className="flex items-center gap-2 p-4 bg-gray-50 rounded-lg">
              <input
                type="text"
                value={shareUrl}
                readOnly
                className="flex-1 bg-transparent text-sm text-gray-600 outline-none"
                placeholder="Link"
              />
              <button
                onClick={handleCopyLink}
                className="p-2 hover:bg-gray-200 rounded transition-colors"
              >
                <Copy className="size-4" />
              </button>
            </div>
          </div>

          <Button
            onClick={handleClose}
            className="w-full bg-green-600 hover:bg-green-700 text-white py-3 rounded-xl transition-colors"
          >
            Ok
          </Button>
        </div>
      </div>
    );
};

export default function CalendarDetailsScreen() {
  const { id } = useParams();
  const { userId } = useAuth();
  const [showShareModal, setShowShareModal] = useState(false);

  const { events, upComingEvents, apiCalendars } = useCalendarDetailsWithEvents({
    calendarId: id,
    usrId: userId,
  });

  const { mutate: updateCalendar } = useUpdateCalendar();
  const navigate = useNavigate();

  const [activeTab, setActiveTab] = useState<AvailableCalendarTabs>("Upcoming");
  const [selectedDate, setSelectedDate] = useState(new Date());

  const handleTabChange = (tab: AvailableCalendarTabs) => {
    setActiveTab(tab);
  };

  const CalendarData = apiCalendars || null;

  const handleShareCalendar = () => {
    setShowShareModal(true);
  };

  // const shareUrl = `${window.location.origin}/calendar/public/${id}`;
  const shareUrl = `${WEBSITE_URL}/calendar/public/${id}`;

  return (
    <div className="flex flex-col w-full h-dvh">
      <div className="justify-between flex items-end">
        <button
          className="pl-8 flex gap-1 items-center"
          onClick={() => navigate(-1)}>
          <ChevronLeft className="size-5 lg:size-6" />
          <p className="text-sm lg:text-base">Back to Calendars</p>
        </button>
        <div className="w-52 lg:w-64">
          <BussinessInfo />
        </div>
      </div>
      
      <div className="flex w-full h-[calc(100dvh-40px)] lg:h-[calc(100dvh-48px)] py-3 overflow-y-auto">
        {/* side bar */}
        <div className="w-64 lg:w-80 flex flex-col pr-3 pt-3 gap-2 overflow-y-auto no-scrollbar">
          <div className="bg-fb-bPrime-100 px-2 py-1 rounded-xl drop-shadow-cardDeatilsShadow flex flex-col gap-1">
            <div className="flex flex-col">
              <p className="font-semibold text-sm md:text-base lg:text-2xl leading-5 -tracking-wide">
                {CalendarData?.calendarName}
              </p>
              <p className="text-xs md:text-sm lg:text-base font-light italic">
                @{CalendarData?.calendarId}
              </p>
              <div className="flex items-center gap-2 mt-2 justify-end">
                <Button
                  className="w-fit h-6 px-3 py-2 text-xs text-black bg-white drop-shadow-buttonShadow hover:bg-white/80"
                  onClick={() =>
                    navigate(`/edit-calendar/${CalendarData?._id}`)
                  }>
                  <img src={EditIcon} alt="" className="size-4" />
                  Edit
                </Button>
                <Button
                  className="w-fit h-6 px-3 py-2 text-xs text-black bg-white drop-shadow-buttonShadow hover:bg-white/80"
                  onClick={handleShareCalendar}>
                  Share Calendar
                </Button>
              </div>
            </div>

            <div className="w-full h-48 rounded-xl overflow-hidden relative">
              <Image src={CalendarData?.picture || ""} />
              <div className="absolute w-full h-full bg-black/60 top-0 left-0" />
            </div>
            <div>
              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="item-1">
                  <div className="flex justify-between">
                    <AccordionTrigger className="text-sm py-1 font-normal flex gap-1 no-underline hover:no-underline items-center !justify-start">
                      <Users className="size-4 !rotate-0" />{" "}
                      {CalendarData?.followers?.length} followers
                    </AccordionTrigger>
                    <div className="flex items-center gap-1 mb-auto mt-1">
                      <label htmlFor="private" className="text-sm">
                        Private
                      </label>
                      <Switch
                        id="private"
                        size="md"
                        checked={CalendarData?.private}
                        onCheckedChange={(checked) => {
                          if (CalendarData?._id) {
                            updateCalendar(
                              { ...CalendarData, private: checked, pictureImage: null, email: CalendarData.email || '', businessUser: true },
                              {
                                onSuccess: () => {
                                  toast.success("Calendar privacy updated!");
                                },
                                onError: (error) => {
                                  toast.error(`Failed to update privacy: ${error.message}`);
                                },
                              }
                            );
                          }
                        }}
                      />
                    </div>
                  </div>
                  <AccordionContent className="py-0">
                    <div className="w-full flex flex-col ">
                      <p className="text-sm font-semibold ">
                        People attending this event
                      </p>
                      <div className="flex flex-col gap-1.5 px-4 py-2">
                        {CalendarData?.followers?.map((row, ind) => {
                          return (
                            <div key={ind} className="flex items-center gap-2">
                              <Image
                                src={row.picture || ""}
                                className="h-5 w-5 overflow-hidden rounded-full"
                              />
                              <p className="text-xs">{row.displayName}</p>
                              <div className="flex gap-2 ml-auto items-center">
                                <img src={MessageIcon} alt="" className="h-4" />
                                <Flag className="size-3.5" />
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-3">
                  <AccordionTrigger className="font-semibold py-1 text-sm text-black/75 no-underline hover:no-underline border-b-0">
                    About the Calendar
                  </AccordionTrigger>
                  <AccordionContent>
                    {CalendarData?.description}
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>
          </div>
          <div className="flex w-full h-full  justify-center items-center">
            <EventsOnDateView selectedCalendarId={id} selectedDate={selectedDate} />
          </div>
        </div>
        
        {/* tab views */}
        <div className="flex flex-col gap-2 py-3 w-[calc(100%-250px)] h-full bg-white rounded-2xl">
          <CalendarTabSwitcher
            defaultTab={activeTab}
            onTabChange={handleTabChange}
          />

          {activeTab === "Upcoming" && (
            <UpComingEvents events={upComingEvents} TodoList={[]} calendar={CalendarData} />
          )}
          {activeTab === "Week" && (
            <WeeklyCalendar events={events} Todos={[]} currentDate={selectedDate} onDateChange={setSelectedDate} />
          )}
          {activeTab === "Month" && <MonthCalendar events={events} Todos={[]} currentDate={selectedDate} setCurrentDate={setSelectedDate} />}
          {activeTab === "Year" && (
            <YearCalendar
              currentYear={selectedDate}
              setCurrentYear={setSelectedDate}
              onClick={setSelectedDate}
            />
          )}
        </div>
      </div>

      {/* Share Calendar Modal */}
      <ShareCalendarModal
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
        calendarName={CalendarData?.calendarName || "Calendar"}
        shareUrl={shareUrl}
      />
    </div>
  );
}