// src/hooks/useEventWithReminders.ts
import { useCreateEvent, useUpdateEvent } from '../api/event/index';
import { reminderScheduler } from '../services/ReminderScheduler';
import { useNotificationsWithReminders } from './useNotificationsWithReminders';

export const useEventWithReminders = () => {
  const createEventMutation = useCreateEvent();
  const updateEventMutation = useUpdateEvent();

  const { requestNotificationPermission } = useNotificationsWithReminders();

  const createEventWithReminder = async (eventData: any) => {
    console.log('[useEventWithReminders] createEventWithReminder called with:', eventData);
    
    try {
      const result = await createEventMutation.mutateAsync(eventData);
      console.log('[useEventWithReminders] API response:', result);
      
      // Add extensive logging here
      console.log('[useEventWithReminders] Checking reminder conditions:', {
        reminderSelected: eventData.reminderSelected,
        reminder: eventData.reminder,
        hasEventId: !!result._id,
        hasTitle: !!eventData.title,
        hasDateTime: !!eventData.dateTime
      });
      
      if (eventData.reminderSelected && eventData.reminder) {
        console.log('[useEventWithReminders] ✅ All conditions met, scheduling reminder...');
        console.log('[useEventWithReminders] Scheduling with params:', {
          eventId: result._id,
          title: eventData.title,
          dateTime: eventData.dateTime,
          reminderTime: eventData.reminder
        });

        // Request notification permission when a reminder is being set
        await requestNotificationPermission();
        const reminderId = reminderScheduler.scheduleReminder(
          result._id,
          eventData.title,
          eventData.dateTime,
          eventData.reminder
        );
        
        console.log('[useEventWithReminders] Reminder scheduled with ID:', reminderId);
        console.log('[useEventWithReminders] Total active reminders:', reminderScheduler.getScheduledReminders().length);
      } else {
        console.log('[useEventWithReminders] ❌ Reminder not scheduled - conditions not met');
      }
      
      return result;
    } catch (error) {
      console.error('[useEventWithReminders] Error in createEventWithReminder:', error);
      throw error;
    }
  };

  const updateEventWithReminder = async (eventId: string, eventData: any) => {
    console.log('[useEventWithReminders] updateEventWithReminder called with:', { eventId, eventData });
    try {
      const result = await updateEventMutation.mutateAsync({ eventId, ...eventData });
      console.log('[useEventWithReminders] API update response:', result);
      
      console.log('[useEventWithReminders] Checking update reminder conditions:', {
        reminderSelected: eventData.reminderSelected,
        reminder: eventData.reminder,
        hasEventId: !!eventId,
        hasTitle: !!eventData.title,
        hasDateTime: !!eventData.dateTime
      });

      // Update reminder scheduling
      if (eventData.reminderSelected && eventData.reminder) {
        console.log('[useEventWithReminders] ✅ All conditions met for update, scheduling reminder...');
        // Request notification permission when a reminder is being updated
        await requestNotificationPermission();
      }
      reminderScheduler.updateEventReminder(
        eventId,
        eventData.title,
        eventData.dateTime,
        eventData.reminder,
        eventData.reminderSelected
      );
      console.log('[useEventWithReminders] Reminder update processed.');
      
      return result;
    } catch (error) {
      console.error('[useEventWithReminders] Error in updateEventWithReminder:', error);
      throw error;
    }
  };

  const deleteEventReminders = (eventId: string) => {
    reminderScheduler.cancelEventReminders(eventId);
  };

  return {
    createEventWithReminder,
    updateEventWithReminder,
    deleteEventReminders,
    isCreating: createEventMutation.isPending,
    isUpdating: updateEventMutation.isPending
  };
};
