import { useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "../endpoints";
import { API } from "@/utils/axios";
import { handleApiResponse } from "@/utils/axios/axiosHelper";
import { StatusCodes } from "@/types/api";

const getIcsFeed = async (calendarId: string) => {
  const response = await API.get(`${API_ENDPOINTS.ics.get}${calendarId}`);
  return handleApiResponse(
    response,
    StatusCodes.OK,
    "Failed to get ICS feed"
  );
};

export const useGetIcsFeed = (calendarId: string) => {
  return useQuery({
    queryKey: ["icsFeed", calendarId],
    queryFn: () => getIcsFeed(calendarId),
    enabled: !!calendarId,
  });
};

const getWebcalFeed = async (calendarId: string) => {
    const response = await API.get(`${API_ENDPOINTS.ics.getWebcal}${calendarId}`);
    return handleApiResponse(
      response,
      StatusCodes.OK,
      "Failed to get webcal feed"
    );
  };
  
  export const useGetWebcalFeed = (calendarId: string) => {
    return useQuery({
      queryKey: ["webcalFeed", calendarId],
      queryFn: () => getWebcalFeed(calendarId),
      enabled: !!calendarId,
    });
  };

  const getSimpleWebcalFeed = async (calendarId: string) => {
    const response = await API.get(`${API_ENDPOINTS.ics.getWebcalevent}${calendarId}`);
    return handleApiResponse(
      response,
      StatusCodes.OK,
      "Failed to get simple webcal feed"
    );
  };

  export const useGetSimpleWebcalFeed = (calendarId: string) => {
    return useQuery({
      queryKey: ["simpleWebcalFeed", calendarId],
      queryFn: () => getSimpleWebcalFeed(calendarId),
      enabled: !!calendarId,
    });
  };
