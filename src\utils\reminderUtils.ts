// src/utils/reminderUtils.ts
import { REMINDER_TIME_OPTIONS } from '../types/reminder';

export const parseReminderTime = (reminderTime: string): number => {
  const option = REMINDER_TIME_OPTIONS[reminderTime as keyof typeof REMINDER_TIME_OPTIONS];
  return option ? option.minutes * 60 * 1000 : 0; // Convert to milliseconds
};

export const calculateTriggerTime = (eventDateTime: string, reminderTime: string): number => {
  const eventTime = new Date(eventDateTime).getTime();
  const reminderOffset = parseReminderTime(reminderTime);
  return eventTime - reminderOffset;
};

export const formatRelativeTime = (timestamp: number): string => {
  const now = Date.now();
  const diff = Math.abs(now - timestamp);
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (minutes < 60) return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
  if (hours < 24) return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
  return `${days} day${days !== 1 ? 's' : ''} ago`;
};

export const generateReminderId = (eventId: string, reminderTime: string): string => {
  return `reminder_${eventId}_${reminderTime}`;
};
