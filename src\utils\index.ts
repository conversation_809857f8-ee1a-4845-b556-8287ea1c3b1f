import { v4 as uuidv4 } from "uuid";
import moment from "moment-timezone";
import {
  addMinutes,
  endOfMonth,
  format,
  isAfter,
  isSameDay,
  isValid,
  isWithinInterval,
  parseISO,
  startOfMonth,
  subMinutes,
} from "date-fns";

import { ToDoStatusValue } from "@/enums";
import { EventModelT, TodoListModelT, ZoneInfo } from "@/types";
import { firebaseErrorMap } from "@/constant/data";

export const formatEventDate = (d: string | Date | null | undefined) => {
  if (!d) {
    console.warn("No date provided:", d);
    return { date: "", day: "" };
  }

  const date = new Date(d);

  if (!isValid(date)) {
    // console.warn("Invalid formateEvent date:", d);
    return { date: "", day: "" };
  }

  if (isNaN(date.getTime())) {
    // console.warn("Invalid date format:", d);
    return { date: "", day: "" };
  }

  const dayFormatter = new Intl.DateTimeFormat("en-GB", { weekday: "long" });
  const dateFormatter = new Intl.DateTimeFormat("en-GB", {
    day: "2-digit",
    month: "short",
    year: "2-digit",
  });

  const day = dayFormatter.format(date);
  const formattedDate = dateFormatter.format(date);

  return { date: formattedDate, day };
};

export const formatEventMonth = (d: string | Date) => {
  const date = new Date(d);
  const dateFormatter = new Intl.DateTimeFormat("en-GB", {
    month: "short",
    year: "2-digit",
  });

  const formattedDate = dateFormatter.format(date);

  return { date: formattedDate };
};

export const formateEventFullDate = (d: string | Date) => {
  const date = new Date(d);
  const dateFormatter = new Intl.DateTimeFormat("en-GB", {
    day: "2-digit",
    month: "long",
    year: "numeric",
  });

  const formattedDate = dateFormatter.format(date);

  return { date: formattedDate };
};

export const currentTimeZone = moment.tz.guess(true);

export const groupTimeZones = () => {
  const timeZones = moment.tz.names();

  const groupedZones: Record<string, { name: string; value: string }[]> = {};

  timeZones.forEach((tz) => {
    const [region, city] = tz.split("/");
    if (!city) return; // Skip if the format is invalid

    // Get UTC offset
    const offset = moment().tz(tz).format("Z");
    const offsetFormatted = `(GMT ${offset})`;

    // const offsetFormatted = moment().tz(tz).format("Z");

    // Initialize region if not exists
    if (!groupedZones[region]) {
      groupedZones[region] = [];
    }

    // Add time zone info
    groupedZones[region].push({
      name: `${tz} ${offsetFormatted}`, // e.g., "America/Antigua GMT-4"
      value: tz, // The original timezone string
    });
  });

  return groupedZones;
};

// Generate time slots from 0 AM to 11 PM
export const timeSlots: string[] = Array.from({ length: 24 }, (_, i) => {
  const twoDigitNum = (num: number) => (num < 10 ? `0${num}` : num);

  const hour = i;
  return hour < 12
    ? `${twoDigitNum(hour)} am`
    : hour === 12
    ? `12 pm`
    : `${twoDigitNum(hour - 12)} pm`;
});

export const generateUUID = (caller: string): string => {
  return `${caller}__${uuidv4()}`;
};

export const filterUpcomingEvents = (events: EventModelT[]): EventModelT[] => {
  const now = new Date();

  return events.filter((event) => isAfter(parseISO(event.end), now));
};

const isSameTimeOnSameDay = (d1: Date, d2: Date) =>
  isSameDay(d1, d2) &&
  d1.getHours() === d2.getHours() &&
  d1.getMinutes() === d2.getMinutes() &&
  d1.getSeconds() === d2.getSeconds() &&
  d1.getTime() === d2.getTime();

export const filterOngoingDateEvents = (
  events: EventModelT[],
  date: Date | string
): EventModelT[] => {
  const targetDate = typeof date === "string" ? parseISO(date) : date;

  return events.filter((event) => {
    const rawStart = parseISO(event.start);
    const rawEnd = parseISO(event.end);

    if (isSameTimeOnSameDay(rawStart, rawEnd)) {
      return false;
    }

    const adjustedStart = addMinutes(rawStart, 2);
    const adjustedEnd = subMinutes(rawEnd, 2);

    return (
      isSameDay(targetDate, adjustedStart) ||
      isSameDay(targetDate, adjustedEnd) ||
      isWithinInterval(targetDate, { start: adjustedStart, end: adjustedEnd })
    );
  });
};

export const filterOngoingMonthEvents = (
  events: EventModelT[],
  date: Date | string
): EventModelT[] => {
  const targetDate = typeof date === "string" ? parseISO(date) : date;

  if (!isValid(targetDate)) {
    console.warn("Invalid ongoing month date:", date);
    return [];
  }
  const monthStart = startOfMonth(targetDate);
  const monthEnd = endOfMonth(targetDate);

  return events.filter((event) => {
    const rawStart = parseISO(event.start);
    const rawEnd = parseISO(event.end);

    // Ignore if it's a zero-duration event
    if (rawStart.getTime() === rawEnd.getTime()) return false;

    // Adjust range slightly if needed
    const adjustedStart = addMinutes(rawStart, 2);
    const adjustedEnd = subMinutes(rawEnd, 2);

    // Keep if event overlaps with target month
    return (
      isWithinInterval(adjustedStart, { start: monthStart, end: monthEnd }) ||
      isWithinInterval(adjustedEnd, { start: monthStart, end: monthEnd }) ||
      (adjustedStart < monthStart && adjustedEnd > monthEnd)
    );
  });
};

export const filterOngoingDateTodos = (
  events: TodoListModelT[],
  date: Date | string
): TodoListModelT[] => {
  const targetDate = typeof date === "string" ? parseISO(date) : date;

  if (!isValid(targetDate)) {
    console.warn("Invalid ongoing date todos:", date);
    return [];
  }

  return events.filter((event) => {
    const rawStart = parseISO(event.date || "");

    const adjustedStart = addMinutes(rawStart, 2);

    return isSameDay(targetDate, adjustedStart);
  });
};

export const filterOngoingMonthTodos = (
  todos: TodoListModelT[],
  date: Date | string
): TodoListModelT[] => {
  const targetDate = typeof date === "string" ? parseISO(date) : date;

  if (!isValid(targetDate)) {
    console.warn("Invalid ongoing month todos date input:", date);
    return [];
  }

  const monthStart = startOfMonth(targetDate);
  const monthEnd = endOfMonth(targetDate);

  return todos.filter((todo) => {
    if (!todo.date || typeof todo.date !== "string") {
      console.warn("Skipping todo with invalid date:", todo);
      return false;
    }

    const todoDate = parseISO(todo.date);
    if (!isValid(todoDate)) {
      console.warn("Skipping todo with invalid parsed date:", todo.date);
      return false;
    }

    return isWithinInterval(todoDate, { start: monthStart, end: monthEnd });
  });
};

export const formatTimeOnly = (input: Date | string): string => {
  const date = typeof input === "string" ? parseISO(input) : input;
  return format(date, "hh:mm a"); // 12-hour format with AM/PM
};

export const convertEventTimesToLocal = (
  events: EventModelT[]
): EventModelT[] => {
  const localTz = moment.tz.guess(); // Detect  local timezone

  console.log(events, "events----");

  return events.map((event) => {
    const { start, end, zone } = event;

    if (event.allDayEvent) {
      const startMoment = moment.tz(start, zone?.tzCode || localTz);

      event.start = startMoment.clone().startOf("day").format(); // 00:00
      event.end = startMoment.clone().endOf("day").format(); // 23:59:59.999

      return event;
    }

    // If the event's timezone is already the local timezone, no conversion needed
    if (zone?.tzCode === localTz) {
      return event; // Return the event as is
    }

    // Convert start and end from event's timezone to local timezone
    event.start = moment.tz(start, zone?.tzCode).tz(localTz).format(); // ISO string
    event.end = moment.tz(end, zone?.tzCode).tz(localTz).format();

    return event; // Return the updated event
  });
};

export const getZoneFromTzCode = (tzCode: string): ZoneInfo => {
  const zoneMoment = moment.tz(tzCode);
  const offset = zoneMoment.format("Z"); // e.g. "+05:30"

  return {
    label: `${tzCode} (GMT${offset})`,
    tzCode,
    name: `(GMT${offset}) ${tzCode.replace("_", " ")}`, // optional: make name more human
    utc: offset,
  };
};

const sampleTitles = [
  "Buy groceries",
  "Finish report",
  "Call plumber",
  "Book flight",
  "Attend yoga",
  "Read a book",
  "Plan budget",
  "Clean garage",
  "Respond to emails",
  "Visit dentist",
  "Update resume",
  "Study TypeScript",
  "Backup files",
  "Water plants",
  "Exercise",
];

const sampleCategories = [
  "Personal",
  "Work",
  "Home",
  "Travel",
  "Health",
  "Finance",
  "Learning",
];

const getRandomItem = <T>(arr: T[]): T =>
  arr[Math.floor(Math.random() * arr.length)];

const getRandomStatus = (): ToDoStatusValue => {
  const values = Object.values(ToDoStatusValue);
  return getRandomItem(values);
};

export const generateTodoData = (): TodoListModelT[] => {
  const todos: TodoListModelT[] = [];
  const today = new Date();

  for (let offset = -30; offset <= 15; offset++) {
    const currentDate = new Date(today);
    currentDate.setDate(today.getDate() + offset);
    const dateStr = currentDate.toISOString().split("T")[0];

    const numberOfTodos = Math.floor(Math.random() * 6); // 0 to 5 todos per day

    for (let i = 0; i < numberOfTodos; i++) {
      const status = getRandomStatus();
      todos.push({
        taskId: uuidv4(),
        task: getRandomItem(sampleTitles),
        comment: "Auto-generated task",
        date: dateStr,
        status,
        // isCompleted: status === ToDoStatusValue.COMPLETED,
        category: getRandomItem(sampleCategories),
        priorityImg: getRandomItem([
          ToDoStatusValue.COMPLETED,
          ToDoStatusValue.NO_IMPORTANT,
          ToDoStatusValue.URGENT,
        ]),
        createdAt: new Date().toDateString(),
      });
    }
  }

  return todos;
};

export const extractFriendlyMessage = (msg: string) => {
  const matched = Object.keys(firebaseErrorMap).find((code) =>
    msg.includes(code)
  );
  return matched ? firebaseErrorMap[matched] : msg;
};

export const getNextHalfHour = (): Date => {
  const now = new Date();
  const minutes = now.getMinutes();
  const nextHalfHour = Math.ceil(minutes / 30) * 30;

  if (nextHalfHour === 60) {
    now.setHours(now.getHours() + 1);
    now.setMinutes(0);
  } else {
    now.setMinutes(nextHalfHour);
  }

  now.setSeconds(0);
  now.setMilliseconds(0);
  return now;
};

export const getPreviousHalfHour = (): Date => {
  const now = new Date();
  const minutes = now.getMinutes();
  const roundedMinutes = Math.floor(minutes / 30) * 30;

  now.setMinutes(roundedMinutes);
  now.setSeconds(0);
  now.setMilliseconds(0);
  return now;
};
