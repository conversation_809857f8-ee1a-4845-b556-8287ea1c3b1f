import { useMemo } from "react";

// import { EventModelT } from "@/types";
import { useGetAllEvents } from "@/api/event";
import { useGetAllCalendar } from "@/api/calendar";
import { expandRecurringEvents, mapRawEventsToModel } from "@/utils/model";
import { convertEventTimesToLocal, filterUpcomingEvents } from "@/utils";
import { useGetAccounDetails } from "@/api/account";

export const useCalendarsWithEvents = (usrId?: string | null, selectedCalendarId?: string | null) => {
  const {
    data: calendars,
    isLoading: loadingCalendars,
    ...calendarQuery
  } = useGetAllCalendar({ usrId: usrId || undefined });
  const { data: userAccountData = null } = useGetAccounDetails({
    userId: usrId || undefined,
  });

  const calendarIds = useMemo(() => {
    if (selectedCalendarId) {
      return [selectedCalendarId];
    }
    return calendars?.map((calendar) => calendar._id) ?? [];
  }, [calendars, selectedCalendarId]);

  const TodoList = useMemo(() => {
    return userAccountData?.accData[0]?.Tasks || [];
  }, [userAccountData]);

  const {
    data: events,
    isLoading: loadingEvents,
    ...eventsQuery
  } = useGetAllEvents({ calendarIds });

  const { upComingEvents, LocalTimeZoneEvents } = useMemo(() => {
    if (!events) {
      return { upComingEvents: [], LocalTimeZoneEvents: [] };
    }

    const localEvents = convertEventTimesToLocal(mapRawEventsToModel(events));
    const allEvents = expandRecurringEvents(localEvents);
    const upcoming = filterUpcomingEvents(allEvents);

    return { upComingEvents: upcoming, LocalTimeZoneEvents: allEvents };
  }, [events]);

  return {
    apiCalendars: calendars,
    apiEvents: events,
    loading: loadingCalendars || loadingEvents,
    calendarQuery,
    eventsQuery,
    events: LocalTimeZoneEvents,
    upComingEvents,
    TodoList,
  };
};
