import { toast } from "sonner";
import { useNavigate } from "react-router";
import { useQueryClient } from "@tanstack/react-query";
import { createContext, useEffect, useState, useContext } from "react";

import AppLoader from "@/components/common/app-loader";

const AuthContext = createContext<{
  handleLogin: (userId: string) => void;
  handleLogout: () => void;
  userId: string | null;
}>({
  handleLogin: () => {},
  handleLogout: () => {},
  userId: null,
});

export const useAuth = () => useContext(AuthContext);

const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [userId, setUserId] = useState<string | null>(null);

  const queryClient = useQueryClient();

  useEffect(() => {
    const userData = localStorage.getItem("authUserId");
    if (userData) {
      setUserId(userData);
    }
    setLoading(false);
  }, []);

  const handleLogin = (userId: string) => {
    localStorage.setItem("authUserId", userId);
    // Clear all cached queries and mutations
    queryClient.clear();

    // Optional: Refetch all active queries
    queryClient.invalidateQueries();
    setUserId(userId);
    setLoading(false);
  };

  const handleLogout = () => {
    try {
      localStorage.clear();
      queryClient.clear();
      setUserId(null);
      toast.success("Logged out successfully!");
      navigate("/login");
    } catch (error) {
      console.error("Failed to log out", error);
      toast.error("Failed to log out!");
    }
  };

  if (loading) return <AppLoader />;

  return (
    <AuthContext.Provider value={{ handleLogout, userId, handleLogin }}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthProvider;
