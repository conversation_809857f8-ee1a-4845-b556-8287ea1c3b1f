export const API_ENDPOINTS = {
  event: {
    getAll: "/api/agendas/fetchBuisnessAgendas",
    create: "/api/agendas/createBusiness",
    update: "/api/agendas/updateBusiness",
    details: "/api/agendas/eventDetailsById",
  },
  calendar: {
    getAll: "/api/calendars/fetchCalendarIdBiz",
    singleOne: "/api/calendars/fetch-biz-calendar",
    create: "/api/calendars/createBiz",
    update: "/api/calendars/update-biz-calendar",
  },
  account: {
    get: "/api/accounts/fetchAccDetailsBu",
    update: "/api/users/update-business-user-details",
    create: "/api/accounts/accSetupBu",
  },
  todo: {
    create: "/api/todoDev/addTask",
    update: "/api/todoDev/updateTask",
    delete: "/api/todoDev/deleteTask",
    updateStatus: "/api/todoDev/updateTaskStatus",
    categoryAddUpdate: "/api/todoDev/updateCategories",
  },
  comment: {
    create: "/api/others/addComent",
    delete: "/api/others/deleteComment",
    report: "/api/others/reportComment",
    edit: "/api/others/editComment",
  },
  reply: {
    create: "/api/others/addReply",
    delete: "/api/others/deleteReply",
    report: "/api/others/reportReply",
    edit: "/api/others/editReply",
  },
  imageUpload: {
    upload: "/api/file/upload",
  },
  notification: {
    getAll: "/api/others/fetchNotificationsByBizUserId",
    markAsRead: "/api/others/markNotificationsAsRead",
  },
  auth: {
    deleteAccount: "/api/users/delete-userBiz",
    login: "/api/users/userDetailsBiz_login",
    signUp: "/api/accounts/signupBiz",
  },
  ai: {
    chatbotEni:"/api/ai/chatbotbut"
  },
  ics: {
    get: "/api/ics/",
    getWebcalevent: "/api/ics/webcal-events/",
    getWebcal: "/api/ics/webcal/",
  },
};
