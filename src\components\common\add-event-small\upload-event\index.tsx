import { useState } from "react";
import { X } from "lucide-react";
import { Link } from "react-router";

import { Button } from "@/components/ui/button";
import DownArrows from "@/assets/images/down-arrows.png";
import DownloadIcon from "@/assets/svg/download-icon.svg";
import AchiveIcon from "@/assets/svg/send-archive-icon.svg";
import { UploadEventFileDialog } from "./components/upload-event-file";

export default function AddEventUploadSmallView({
  onClose,
}: {
  onClose: () => void;
}) {
  const [_, setConformationPopup] = useState({
    open: false,
    title: "",
    subTitle: "",
  });

  const handleSetConformation = ({
    title,
    subTitle,
  }: {
    title: string;
    subTitle: string;
  }) => {
    setConformationPopup({ open: true, title: title, subTitle: subTitle });
  };

  return (
    <div className="flex flex-1 gap-2 flex-col overflow-y-auto no-scrollbar">
      <div className="px-2 flex items-center justify-between">
        <div className="flex items-center gap-1">
          <Link to={"/add-event-upload"}>
            <img src={AchiveIcon} className="size-5" />
          </Link>
          <p className="text-black font-bold text-xl">New Event</p>
        </div>
        <Button
          variant={"ghost"}
          size={"icon"}
          className="!h-7 !w-7 rounded-full"
          onClick={onClose}>
          <X />
        </Button>
      </div>
      <div className="flex flex-col p-4 gap-4">
        <div className="flex flex-col gap-2">
          <p className="text-black font-bold text-xl">Upload Events</p>
          <p className="text-sm leading-4">
            If you have multiple events you can upload from template it all at
            once follow the steps below
          </p>
        </div>
        <div className="flex flex-col gap-8 ">
          <div className="fex flex-col gap-2">
            <div className="flex gap-2 items-center">
              <div className="h-10 w-10 rounded-lg bg-fb-bPrime-50 flex justify-center items-center text-black text-2xl">
                1
              </div>
              <p className="text-fb-neutral-700 text-sm">
                Download the template below
              </p>
            </div>
            <div className="">
              <img src={DownArrows} className="w-full h-7 py-1 select-none" />
              <Button
                variant={"default"}
                className="bg-fb-bPrime-50 hover:bg-gray-300 text-black font-semibold drop-shadow-buttonShadow rounded-full h-8 w-full">
                <img src={DownloadIcon} alt="" className="w-5 h-5 text-white" />{" "}
                Download template
              </Button>
            </div>
          </div>
          <div className="flex flex-col gap-3">
            <div className="flex gap-2 items-center">
              <div className="h-10 w-10 rounded-lg bg-fb-bPrime-50 flex justify-center items-center text-black text-2xl">
                2
              </div>
              <p className="text-fb-neutral-700">Fill in the data</p>
            </div>
            <div className="flex gap-2 items-center">
              <div className="h-10 w-10 rounded-lg bg-fb-bPrime-50 flex justify-center items-center text-black text-2xl">
                3
              </div>
              <p className="text-fb-neutral-700 text-sm">
                Save and Upload the file as .XL, CSV
              </p>
            </div>

            <UploadEventFileDialog setConfomation={handleSetConformation} />
          </div>
        </div>
      </div>
      <Button className="bg-fb-bPrime-600 !rounded-12px h-7 sticky bottom-0 mt-auto">
        Add Event
      </Button>
    </div>
  );
}
