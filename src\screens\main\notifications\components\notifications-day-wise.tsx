import { formatEventDate } from "@/utils";
import { NotificationModelT } from "@/types/api";
import { useGetInfiniteNotifications } from "@/api/notification";
import { NotificationFormT } from "@/types/api";
import { useRef, useCallback } from "react";
import AppLoader from "@/components/common/app-loader";
import { useAuth } from "@/context/AuthContext";
import { Notification } from "@/hooks/useNotificationsWithReminders"; // Import the Notification type from the hook

type CombinedNotification = NotificationModelT | Notification;

type GroupedData = {
  date: string;
  data: CombinedNotification[];
};

const groupNotificationsByDate = (
  datas: CombinedNotification[]
): GroupedData[] => {
  const grouped = datas.reduce<Record<string, CombinedNotification[]>>((acc, notification) => {
    const dateKey = new Date((notification as Notification).timestamp || (notification as NotificationModelT).date || "").toDateString();
    if (!acc[dateKey]) {
      acc[dateKey] = [];
    }
    acc[dateKey].push(notification);
    return acc;
  }, {});

  return Object.keys(grouped).map(date => ({
    date,
    data: grouped[date].sort((a, b) => {
      const dateA = new Date((a as Notification).timestamp || (a as NotificationModelT).date || "").getTime();
      const dateB = new Date((b as Notification).timestamp || (b as NotificationModelT).date || "").getTime();
      return dateB - dateA;
    })
  })).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
};

export default function NotificaitonsDayWise({ reminderNotifications, deleteNotification }: { reminderNotifications: Notification[], deleteNotification: (id: string) => void }) {
  const { userId } = useAuth();

  const payload: Omit<NotificationFormT, "page"> = {
    bizUserId: userId,
    limit: 20,
    sortBy: "date",
    sortOrder: "desc",
  };

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading } =
    useGetInfiniteNotifications(payload);

  const observer = useRef<IntersectionObserver | null>(null);
  const lastElementRef = useCallback(
    (node: HTMLDivElement | null) => {
      if (isFetchingNextPage) return;
      if (observer.current) observer.current.disconnect();

      observer.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && hasNextPage) {
          fetchNextPage();
        }
      });

      if (node) observer.current.observe(node);
    },
    [fetchNextPage, hasNextPage, isFetchingNextPage]
  );
  const notifications = data?.pages.flatMap((p) => p.notifications) ?? [];

  const allNotifications: CombinedNotification[] = [...notifications, ...reminderNotifications];

  const newData = allNotifications.filter((item) => item !== undefined);

  const groupNotifications = groupNotificationsByDate(newData || []);

  return (
    <div className="flex flex-col gap-3 overflow-y-auto no-scrollbar">
      {isLoading ? (
        <AppLoader />
      ) : (
        groupNotifications.map((row, groupIdx) => {
          const date = formatEventDate(row.date);

          return (
            <div key={groupIdx} className="flex flex-col gap-2 mb-1">
              <div className="flex gap-1 items-end border-b border-fb-bPrime-hgts">
                <p className="font-semibold text-base">{date.date},</p>
                <p className="font-light text-xs">({date.day})</p>
              </div>
              {row.data.map((not, notifIdx) => {
                const isLast =
                  groupIdx === groupNotifications.length - 1 &&
                  notifIdx === row.data.length - 1;
                return (
                  <div
                    ref={isLast ? lastElementRef : null}
                    className="pr-2 flex gap-1"
                    key={notifIdx}>
                    <NotificationView data={not} key={notifIdx} deleteNotification={deleteNotification} />
                  </div>
                );
              })}
            </div>
          );
        })
      )}
    </div>
  );
}

const NotificationView = ({ data, deleteNotification }: { data: CombinedNotification, deleteNotification: (id: string) => void }) => {
  const isReminder = data.type === 'reminder';

  const newDate = new Date(isReminder ? (data as Notification).timestamp : (data as NotificationModelT).date || "");
  const time = newDate.toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  });

  const notificationId = isReminder ? (data as Notification).id : (data as NotificationModelT)._id;

  return (
    <div className="flex flex-col md:flex-row gap-0.5 md:gap-3 w-full">
      <div className="md:py-2 ml-auto flex h-full items-end">
        <p className="text-xs -tracking-wide whitespace-nowrap">{time}</p>
      </div>
      <div className={`p-2 px-3 rounded-xl flex w-full gap-4 bg-fb-neutral-50 shadow-cardInnerShadow items-center drop-shadow-cardShadow ${isReminder ? 'reminder-notification-card' : ''}`}>
        <div className="w-9 h-9 min-w-9 min-h-9 lg:w-10 lg:h-10 rounded-full overflow-hidden ">
          {isReminder ? (
            <img src="/icons/reminder.png" className="w-full h-full object-cover rounded-full" alt="Reminder Icon" />
          ) : (
            <img src={(data as NotificationModelT).picture} className="w-full h-full object-cover rounded-full" alt="User Avatar" />
          )}
        </div>
        <div>
          <div className="flex items-center gap-2">
            <p className="text-xs lg:text-sm font-medium leading-4">
              {isReminder ? (data as Notification).title : (data as NotificationModelT).displayName} {"  "}
            </p>
          </div>
          {isReminder ? (
            <p className="text-xxs font-light">{(data as Notification).message}</p>
          ) : (
            <>
              {(data as NotificationModelT).type === "follow" && (
                <p className="text-xxs font-light">
                  New followers for{" "}
                  <span className="text-fb-uPrime-500">{(data as NotificationModelT).calendarName}</span>
                </p>
              )}
              {(data as NotificationModelT).type === "RSVP" && (
                <p className="text-xxs font-light">
                  responses to{" "}
                  <span className="text-fb-uPrime-500">{(data as NotificationModelT).calendarName}</span>
                </p>
              )}
              {(data as NotificationModelT).type === "Like" && (
                <p className="text-xxs font-light">
                  liked your{" "}
                  <span className="text-fb-uPrime-500">{(data as NotificationModelT).calendarName}</span>
                </p>
              )}
              {(data as NotificationModelT).type === "unfollow" && (
                <p className="text-xxs font-light">
                  unfollowed your{" "}
                  <span className="text-fb-uPrime-500">{(data as NotificationModelT).calendarName}</span>
                </p>
              )}
            </>
          )}
        </div>
        <button
          className="ml-auto text-gray-500 hover:text-gray-700 focus:outline-none"
          onClick={(e) => {
            e.stopPropagation(); // Prevent parent click handler from firing
            if (notificationId) {
              deleteNotification(notificationId);
            }
          }}
        >
          X
        </button>
      </div>
    </div>
  );
};
