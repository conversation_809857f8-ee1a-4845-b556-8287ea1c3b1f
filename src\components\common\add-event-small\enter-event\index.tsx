import { PdfIcon } from "@/assets/svg/icons";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Link } from "react-router";
import AchiveIcon from "@/assets/svg/send-archive-icon.svg";
import { toast } from "sonner";
import { FormEvent, useState } from "react";
import { FileWithPreview } from "@/types";
import { Image } from "@/components/ui/image";
import PlaceholderImg from "@/assets/images/add-placeholder.jpg";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { X } from "lucide-react";
import { cn } from "@/lib/utils";
import { Switch } from "@/components/ui/switch";
import { DateTimePicker } from "../../date-time-picker";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";


import { useCalendarsWithEvents } from "@/hook/useCalendarsWithEvents";
import {
  currentTimeZone,
  generateUUID,
  getPreviousHalfHour,
  getZoneFromTzCode,
} from "@/utils";
import { CreatEventFormT } from "@/types/api";
import { rawEventFormToModel } from "@/utils/model";
import { useCreateEvent } from "@/api/event";
import { useAuth } from "@/context/AuthContext";
import { addHours, addMonths } from "date-fns";
import { useCustomDropzone, FILE_TYPES } from "@/hook/useCustomDropzone";
import { useNotificationsWithReminders } from "@/hooks/useNotificationsWithReminders";

const ReminderEventTime = [
  { name: "5 Mins", value: "5-min" },
  { name: "10 Mins", value: "10-min" },
  { name: "15 Mins", value: "15-min" },
  { name: "30 Mins", value: "30-min" },
  { name: "1 Hour", value: "1-hour" },
  { name: "1 Day", value: "1-day" },
];

const RepeatEventTime = [
  { name: "Never", value: "Never" },
  { name: "Daily", value: "Daily" },
  { name: "Weekly", value: "Weekly" },
  { name: "Monthly", value: "Monthly" },
];
const RepeatEveryTime = Array.from({ length: 30 }, (_, i) => ({
  name: `${i + 1}`,
  value: `${i + 1}`,
}));

export default function AddEventEnterSmallView({
  onClose,
  openUploadView,
}: {
  onClose: () => void;
  openUploadView: () => void;
}) {
  const { userId } = useAuth();

  const { mutate, isPending } = useCreateEvent();
  const { requestNotificationPermission } = useNotificationsWithReminders();
  const [eventImage, setEventImage] = useState<FileWithPreview[]>([]);
  const [attachmentPdf, setAttachmentPdf] = useState<FileWithPreview[]>([]);

  const { apiCalendars } = useCalendarsWithEvents(userId);

  const [eventName, setEventName] = useState("");
  const [chooseCalendar, setChooseCalendar] = useState<string[]>([]);
  const [reminderTime, setReminderTime] = useState<string | null>("15-min");
  const [isReminderActive, setIsReminderActive] = useState(true);
  const [repeatTime, setRepeatTime] = useState<string | null>(null);
  const [repeatEndDate, setRepeatEndDate] = useState<Date | null>(null);
  const [repeatEveryValue, setRepeatEveryValue] = useState<string>("1");
  const [isAllDayEvent, setIsAllDayEvent] = useState(false);
  const [attachedLinks, setAttachedLinks] = useState<string[]>([""]);

  const [location, setLocation] = useState("");
  const [description, setDescription] = useState("");

  const [startDate, setStartDate] = useState<Date>(() => getPreviousHalfHour());
  const [endDate, setEndDate] = useState<Date>(() => {
    const start = getPreviousHalfHour();
    start.setHours(start.getHours() + 1);
    return start;
  });

  const handleLinkChange = (index: number, value: string) => {
    const newLinks = [...attachedLinks];
    newLinks[index] = value;
    setAttachedLinks(newLinks);
  };

  const handleLinkRemove = (index: number) => {
    const updated = attachedLinks.filter((_, i) => i !== index);
    setAttachedLinks(updated);
  };

  const handleAddLink = () => {
    setAttachedLinks([...attachedLinks, ""]);
  };

  const { getRootProps, getInputProps } = useCustomDropzone({
    acceptedFileTypes: FILE_TYPES.IMAGES,
    maxFiles: 1,
    maxSize: 1 * 1024 * 1024, // 1MB
    multiple: false,
    showToastOnError: true,
    customErrorMessages: {
      "file-invalid-type": "Please upload only image files for event cover",
      "file-too-large": "Event image is too large",
      "too-many-files": "You can only upload one event image",
    },
    onFilesAccepted: (acceptedFiles) => {
      setEventImage(
        acceptedFiles.map((file) =>
          Object.assign(file, {
            preview: URL.createObjectURL(file),
          })
        )
      );
    },
    // No onFilesRejected - errors handled automatically
  });

  const { getRootProps: getRootPropsOfPdf, getInputProps: getInputPropsOfPdf } =
    useCustomDropzone({
      acceptedFileTypes: FILE_TYPES.DOCUMENTS,
      maxFiles: 1,
      maxSize: 1 * 1024 * 1024, // 1MB
      multiple: false,
      showToastOnError: true,
      customErrorMessages: {
        "file-invalid-type": "Please upload only PDF files",
        "file-too-large": "PDF file is too large",
        "too-many-files": "You can only upload one PDF file",
      },
      onFilesAccepted: (acceptedFiles) => {
        setAttachmentPdf(
          acceptedFiles.map((file) =>
            Object.assign(file, {
              preview: URL.createObjectURL(file),
            })
          )
        );
      },
      // No onFilesRejected - errors handled automatically
    });

  const handleCreateEvent = (e: FormEvent) => {
    e.preventDefault();

    if (!chooseCalendar.length || !apiCalendars) {
      toast.info("At least one calendar is required");
      return;
    }

    // Create event for each selected calendar
    chooseCalendar.forEach(calendarId => {
      const calendar = apiCalendars?.find((cal) => cal._id === calendarId);

      const FormData: CreatEventFormT = {
        _id: generateUUID("bizAgenda"),
        calId: calendarId,
        calendarName: calendar?.calendarName || "",
        end: endDate,
        start: startDate,
        event: true,
        note: false,
        title: eventName,
        userType: "businessUser",
        usrId: userId || "",
        zone: getZoneFromTzCode(currentTimeZone),
        allDayEvent: isAllDayEvent,
        blocked: false,
        deleted: false,
        location,
        description,
        reminder: reminderTime,
        reminderSelected: !!reminderTime,
        frequency: repeatTime === "Never" ? undefined : repeatTime,
        days: [], // Assuming no specific day selection for now
        repeat: repeatTime !== "Never" && repeatTime !== null,
        repeatEvery:
          repeatTime === "Never" || repeatTime === null
            ? undefined
            : parseInt(repeatEveryValue),
        repeatEndDate:
          repeatTime === "Never" || repeatTime === null || repeatEndDate === null
            ? undefined
            : repeatEndDate,
        picture: "",
        profileImage: eventImage[0] || null,
        link: attachedLinks,
      };

      const NewData = rawEventFormToModel(FormData);

      mutate(NewData, {
        onSuccess() {
          toast.success(`Event created in ${calendar?.calendarName}`);
        },
        onError(error) {
          toast.error(
            `Failed to create event in ${calendar?.calendarName}: ${error.message}`
          );
        },
      });
    });

    toast.success(
      `Event created in ${chooseCalendar.length} calendar${
        chooseCalendar.length > 1 ? "s" : ""
      }`
    );
    onClose();
  };

  return (
    <form
      onSubmit={handleCreateEvent}
      className="flex flex-1 gap-2 flex-col overflow-y-auto no-scrollbar ">
      <div className="px-2 flex items-center justify-between">
        <div className="flex items-center gap-1">
          <Link to={"/add-event"}>
            <img src={AchiveIcon} className="size-5" />
          </Link>
          <p className="text-black font-bold text-xl">New Event</p>
        </div>
        <Button
          type="button"
          variant={"ghost"}
          size={"icon"}
          className="!h-7 !w-7 rounded-full"
          onClick={onClose}>
          <X />
        </Button>
      </div>
      <div className="border border-fb-neutral-40 rounded-12px p-2 flex flex-col gap-1.5 justify-around items-center">
        <p className="text-fb-neutral-700 text-xs">
          Add multiple events at a time
        </p>
        <Button
          type="button"
          className="bg-fb-bPrime-500 h-8 text-xs w-full text-white"
          onClick={openUploadView}>
          <PdfIcon className="text-white size-7" />
          Upload Events from File
        </Button>
      </div>
      <p className="text-center">OR</p>{" "}
      <div className="flex flex-col w-full">
        {eventImage.length > 0 ? (
          <div className="w-full h-32 rounded-12px overflow-hidden relative">
            <Image src={eventImage[0]?.preview} fallbackSrc={PlaceholderImg} />
            <Button
              type="button"
              variant={"ghost"}
              size={"icon"}
              className="absolute top-2 right-2 h-6 w-6"
              onClick={() => setEventImage([])}>
              <X className="text-fb-warn-600" />
            </Button>
          </div>
        ) : (
          <div
            {...getRootProps({ className: "dropzone" })}
            className="bg-fb-neutral-200 rounded-12px overflow-hidden h-32 flex justify-center items-center cursor-pointer relative">
            <input {...getInputProps()} />
            <Image src={PlaceholderImg} />
            <div className="bg-fb-bPrime-placeholder/50 absolute top-0 left-0 w-full h-full flex justify-center items-center">
              <p className="text-lg font-semibold">Add Image</p>
            </div>
          </div>
        )}
      </div>
      <div className="flex flex-col gap-1">
        <Label htmlFor="EventName" className="text-fb-neutral-400 pl-3">
          Event Name
        </Label>

        <Input
          required
          id="EventName"
          className="!rounded-12px border-fb-neutral-400"
          placeholder="Name"
          value={eventName}
          onChange={(e) => {
            setEventName(e.target.value);
          }}
        />
      </div>
      <div className="flex flex-col gap-1 border-b border-fb-neutral-200 pb-2">
        <Label className="text-fb-neutral-400 pl-3 ">Choose Calendar(s)</Label>
        <div className="grid grid-cols-3 gap-x-2 gap-y-1 mb-1">
          {apiCalendars?.slice(0, 6).map((cal, ind) => {
            return (
              <div
                onClick={() => {
                  setChooseCalendar((prev) => {
                    if (prev.includes(cal._id)) {
                      return prev.filter((id) => id !== cal._id);
                    } else {
                      return [...prev, cal._id];
                    }
                  });
                }}
                key={ind}
                className={cn(
                  " border rounded-8px cursor-pointer h-6 px-1 gap-1 flex items-center",
                  chooseCalendar.includes(cal._id)
                    ? "bg-fb-bPrime-100 border-fb-neutral-900 text-fb-neutral-800"
                    : "bg-fb-neutral-50 border-fb-neutral-500 text-fb-neutral-600"
                )}>
                <div
                  className={cn(
                    "w-3 h-3 min-w-3 rounded-full",
                    chooseCalendar.includes(cal._id)
                      ? "bg-fb-bPrime-500"
                      : "bg-fb-neutral-400"
                  )}
                />
                <p className="truncate text-xs font-semibold">
                  {cal.calendarName}
                </p>
              </div>
            );
          })}
        </div>
        {apiCalendars && apiCalendars.length > 6 && (
          <Accordion type="single" collapsible>
            <AccordionItem value="item-1" className="border-none">
              <AccordionContent className="p-0">
                <div className="grid grid-cols-3 gap-x-2 gap-y-1 mt-1">
                  {apiCalendars.slice(6).map((cal, ind) => (
                    <div
                      onClick={() => {
                        setChooseCalendar((prev) => {
                          if (prev.includes(cal._id)) {
                            return prev.filter((id) => id !== cal._id);
                          } else {
                            return [...prev, cal._id];
                          }
                        });
                      }}
                      key={ind}
                      className={cn(
                        " border rounded-8px cursor-pointer h-6 px-1 gap-1 flex items-center",
                        chooseCalendar.includes(cal._id)
                          ? "bg-fb-bPrime-100 border-fb-neutral-900 text-fb-neutral-800"
                          : "bg-fb-neutral-50 border-fb-neutral-500 text-fb-neutral-600"
                      )}>
                      <div
                        className={cn(
                          "w-3 h-3 min-w-3 rounded-full",
                          chooseCalendar.includes(cal._id)
                            ? "bg-fb-bPrime-500"
                            : "bg-fb-neutral-400"
                        )}
                      />
                      <p className="truncate text-xs font-semibold">
                        {cal.calendarName}
                      </p>
                    </div>
                  ))}
                </div>
              </AccordionContent>
              <AccordionTrigger className="justify-end gap-2 text-sm text-fb-neutral-400 p-0 hover:no-underline">
                More
              </AccordionTrigger>
            </AccordionItem>
          </Accordion>
        )}
      </div>
      <div>
        <div className="flex items-center space-x-2">
          <Label htmlFor="all-day">All day</Label>
          <Switch
            id="all-day"
            size="md"
            checked={isAllDayEvent}
            onCheckedChange={(e) => {
              setIsAllDayEvent(e);
            }}
          />
        </div>
        <div className="w-full flex flex-col gap-0.5 items-center justify-center">
          <div className="flex gap-1 items-center">
            <DateTimePicker
              date={startDate}
              setDate={(e) => {
                setStartDate(e);
                setEndDate(addHours(e, 1));
              }}
              isSmallView
              showDay={false}
            />
            <div className="w-12 h-0.5 rounded-full bg-black/70" />
            <DateTimePicker
              date={endDate}
              setDate={(e) => {
                if (e.getTime() < startDate.getTime()) {
                  toast.info("End date cannot be before start date/time");
                  return;
                }
                setEndDate(e);
              }}
              isSmallView
              showDay={false}
            />
          </div>
        </div>
      </div>
      <div className="flex gap-2 items-center justify-end">
        <div className="flex items-center gap-2">
          <Label
            htmlFor="reminder-switch"
            className="text-fb-neutral-600 font-semibold text-sm">
            Reminder
          </Label>
          <Switch
            id="reminder-switch"
            size="md"
            checked={isReminderActive}
            onCheckedChange={async (checked) => {
              setIsReminderActive(checked);
              if (checked) {
                const permission = await requestNotificationPermission();
                if (permission === "granted") {
                  setReminderTime("15-min");
                } else {
                  setIsReminderActive(false);
                  toast.error(
                    "Notification permission denied. Please enable it in your browser settings to receive reminders."
                  );
                }
              } else {
                setReminderTime(null);
              }
            }}
          />
        </div>

        {isReminderActive && (
          <Select
            value={reminderTime || undefined}
            onValueChange={setReminderTime}>
            <SelectTrigger className="w-fit bg-fb-neutral-100 border-fb-neutral-500 !rounded-12px h-8">
              <SelectValue placeholder="Select Reminder" />
            </SelectTrigger>
            <SelectContent>
              {ReminderEventTime.map((remin, ind) => {
                return (
                  <SelectItem value={remin.value} key={ind}>
                    {remin.name}
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        )}
      </div>
      <div>
        <Accordion type="single" collapsible>
          <AccordionItem value="item-1">
            <AccordionTrigger className=" gap-2 text-sm text-black p-0">
              More Information
            </AccordionTrigger>
            <AccordionContent className="gap-2 flex flex-col">
              <div className="flex flex-col">
                <div className=" ">
                  <p className="text-fb-neutral-600 font-semibold text-sm">
                    Description :
                  </p>
                </div>
                <div className=" gap-1">
                  <Textarea
                    value={description}
                    onChange={(e) => {
                      setDescription(e.target.value);
                    }}
                    className="!rounded-12px border-fb-neutral-400 resize-none"
                    placeholder="Description"
                  />
                </div>
              </div>
              <Separator />
              <div className="flex flex-col">
                <div className="">
                  <p className="text-fb-neutral-600 font-semibold text-sm">
                    Repeat this event :
                  </p>
                </div>
                <div className=" grid grid-cols-4 gap-1">
                  {RepeatEventTime.map((remind, ind) => {
                    return (
                      <div
                        className={cn(
                          "h-9 rounded-12px border cursor-pointer flex items-center justify-center text-sm",
                          repeatTime === remind.value
                            ? "text-fb-neutral-700 border-fb-neutral-600 bg-fb-neutral-100"
                            : "text-fb-neutral-600 border-fb-neutral-400 bg-white"
                        )}
                        key={ind}
                        onClick={() => {
                          setRepeatTime(remind.value);
                          // Set repeat end date to 3 months from current date when repeat is selected
                          if (remind.value !== "Never") {
                            setEndDate(addMonths(new Date(), 3));
                          }
                        }}>
                        {remind.name}
                      </div>
                    );
                  })}
                </div>
              </div>
              {repeatTime !== "Never" && repeatTime !== null && (
                <>
                  <Separator />
                  <div className="flex flex-col">
                    <div className="">
                      <p className="text-fb-neutral-600 font-semibold text-sm">
                        Repeat Every :
                      </p>
                    </div>
                    <div className="col-span-5">
                      <Select
                        onValueChange={setRepeatEveryValue}
                        value={repeatEveryValue}>
                        <SelectTrigger className="w-[180px]">
                          <SelectValue placeholder="Select a number" />
                        </SelectTrigger>
                        <SelectContent>
                          {RepeatEveryTime.map((item) => (
                            <SelectItem key={item.value} value={item.value}>
                              {item.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="flex flex-col">
                    <div className="">
                      <p className="text-fb-neutral-600 font-semibold text-sm">
                        Repeat End Date :
                      </p>
                    </div>
                    <div className="col-span-5">
                      <DateTimePicker
                        date={repeatEndDate || undefined}
                        setDate={setRepeatEndDate}
                        isSmallView
                        showDay={true}
                        disableTimeSelection={true}
                      />
                    </div>
                  </div>
                </>
              )}
              <Separator />
              <div className="flex flex-col">
                <div className="">
                  <p className="text-fb-neutral-600 font-semibold text-sm">
                    Location :
                  </p>
                </div>
                <div className="gap-2 flex flex-col">
                  <div className="flex items-center relative">
                    <Input
                      value={location}
                      onChange={(e) => setLocation(e.target.value)}
                      className="!rounded-12px border-fb-neutral-400"
                      placeholder="Location"
                    />
                  </div>
                </div>
              </div>
              <Separator />
              <div className="flex flex-col">
                <div className="">
                  <p className="text-fb-neutral-600 font-semibold text-sm">
                    Add Links
                  </p>
                </div>
                <div className=" gap-1 flex flex-col">
                  {attachedLinks.map((links, ind) => {
                    return (
                      <div className="relative flex items-center" key={ind}>
                        <Input
                          key={ind}
                          value={links}
                          type="text"
                          className="!rounded-12px border-fb-neutral-400 pr-8"
                          placeholder="Enter Link"
                          onChange={(e) => {
                            handleLinkChange(ind, e.target.value);
                          }}
                        />
                        <Button
                          type="button"
                          variant={"ghost"}
                          size={"icon"}
                          className="absolute right-1 !h-7 !w-7 rounded-full"
                          onClick={() => handleLinkRemove(ind)}>
                          <X />
                        </Button>
                      </div>
                    );
                  })}
                  <Button
                    type="button"
                    variant={"outline"}
                    className="!rounded-12px border-fb-neutral-400 text-fb-neutral-600 h-8"
                    onClick={handleAddLink}>
                    Add a New Link
                  </Button>
                </div>
              </div>
              <Separator />
              <div className="flex flex-col">
                <div className=" ">
                  <p className="text-fb-neutral-600 font-semibold text-sm">
                    Attach Documents
                  </p>
                </div>
                <div className=" gap-2 flex flex-col">
                  {attachmentPdf.length > 0 ? (
                    <div>
                      <Button
                        size={"sm"}
                        variant={"ghost"}
                        type="button"
                        className="rounded-full h-7 text-fb-neutral-600"
                        onClick={() => {
                          setAttachmentPdf([]);
                        }}>
                        <X className="size-4" />
                        {attachmentPdf[0].name}
                      </Button>
                    </div>
                  ) : (
                    <div
                      {...getRootPropsOfPdf()}
                      className="w-28 min-w-36 lg:min-w-64 h-7 bg-fb-neutral-200 rounded-xl flex flex-col justify-center items-center gap-2 relative cursor-pointer">
                      <input {...getInputPropsOfPdf()} />

                      <p className="text-sm font-light text-fb-neutral-600">
                        Attach Pdf
                      </p>
                    </div>
                  )}
                </div>
              </div>
              {/* <Separator />
              <div className="flex flex-col gap-2">
                <div className="">
                  <p className="text-fb-neutral-600 font-semibold text-sm">
                    User Interactions
                  </p>
                </div>
                <div className=" gap-2 flex flex-col">
                  <div className=" flex items-center gap-3 justify-between">
                    <Label
                      htmlFor="option-RSVP"
                      className="text-sm -tracking-wider font-normal flex gap-0.5 text-center cursor-pointer text-fb-neutral-600">
                      Provide RSVP
                      <img src={InfoIcon} alt="" className="w-3 h-3" />
                    </Label>
                    <Switch id="option-RSVP" size="md" />
                  </div>
                  <div className=" flex items-center gap-3 justify-between">
                    <Label
                      htmlFor="allow-comments"
                      className="text-sm  -tracking-wider font-normal flex gap-0.5 text-center cursor-pointer text-fb-neutral-600">
                      Allow Comments
                      <img src={InfoIcon} alt="" className="w-3 h-3" />
                    </Label>
                    <Switch id="allow-comments" size="md" />
                  </div>
                  <div className=" flex items-center gap-3 justify-between">
                    <Label
                      htmlFor="RSVP-comments"
                      className="text-sm -tracking-wider font-normal flex gap-0.5 cursor-pointer text-fb-neutral-600">
                      Allow Users see likes
                      <img src={InfoIcon} alt="" className="w-3 h-3" />
                    </Label>
                    <Switch id="RSVP-comments" size="md" />
                  </div>
                </div>
              </div> */}
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
      <Button
        disabled={isPending}
        type="submit"
        className="bg-fb-bPrime-600 !rounded-12px h-7 sticky bottom-1 mt-auto">
        Add Event
      </Button>
    </form>
  );
}
