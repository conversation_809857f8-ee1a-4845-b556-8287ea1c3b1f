/// <reference types="vite/client" />
interface ImportMetaEnv {
    readonly VITE_FIREBASE_API_KEY: string;
    // add other VITE_ vars if needed
    readonly VITE_FIREBASE_APP_DOMAIN: string;
    readonly VITE_FIREBASE_PROJECT_ID: string;
    readonly VITE_FIREBASE_STORAGE_BUCKET: string;
    readonly VITE_FIREBASE_MSG_ID: string;
    readonly VITE_FIREBASE_APP_ID: string;
    readonly VITE_WEBSITE_URL:string;
    readonly VITE_BASE_URL: string;
    // ...
  }
  interface ImportMeta {
    readonly env: ImportMetaEnv;
  }