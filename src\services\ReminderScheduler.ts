// src/services/ReminderScheduler.ts - Debug Version
import { ScheduledReminder, ReminderNotification } from '../types/reminder';
import { 
  calculateTriggerTime, 
  generateReminderId, 
  formatRelativeTime 
} from '../utils/reminderUtils';

class ReminderScheduler {
  private reminders = new Map<string, ScheduledReminder>();
  private notificationCallbacks = new Set<(notification: ReminderNotification) => void>();
  private debugMode = true; // Enable for debugging

  constructor() {
    this.debug('ReminderScheduler initialized');
    this.loadRemindersFromStorage();
    this.scheduleExistingReminders();
    this.setupVisibilityListener();
    this.testNotificationSupport();
  }

  private debug(message: string, data?: any) {
    if (this.debugMode) {
      console.log(`[ReminderScheduler] ${message}`, data || '');
    }
  }

  private testNotificationSupport() {
    this.debug('Testing notification support...');
    this.debug('Notification in window:', 'Notification' in window);
    this.debug('Current permission:', Notification.permission);
    this.debug('HTTPS:', window.location.protocol === 'https:');
    this.debug('Localhost exception:', window.location.hostname === 'localhost');
  }

  onNotification(callback: (notification: ReminderNotification) => void) {
    this.notificationCallbacks.add(callback);
    return () => this.notificationCallbacks.delete(callback);
  }

  scheduleReminder(
    eventId: string,
    eventTitle: string,
    eventDateTime: string,
    reminderTime: string
  ): string {
    const id = generateReminderId(eventId, reminderTime);
    const triggerAt = calculateTriggerTime(eventDateTime, reminderTime);
    const now = Date.now();
    
    this.debug(`Scheduling reminder for "${eventTitle}"`, {
      eventDateTime,
      reminderTime,
      triggerAt: new Date(triggerAt).toLocaleString(),
      delayMs: triggerAt - now,
      delayMinutes: Math.round((triggerAt - now) / (1000 * 60))
    });

    // Don't schedule if trigger time is in the past
    if (triggerAt <= now) {
      this.debug(`WARNING: Reminder trigger time is in the past!`, {
        triggerAt: new Date(triggerAt).toLocaleString(),
        now: new Date(now).toLocaleString()
      });
      return id;
    }

    // Cancel existing reminder
    this.cancelReminder(id);

    const reminder: ScheduledReminder = {
      id,
      eventId,
      eventTitle,
      eventDateTime,
      reminderTime,
      triggerAt
    };

    // Test immediate notification (for debugging)
    if (this.debugMode && (triggerAt - now) < 10000) { // Less than 10 seconds
      this.debug('DEBUG: Scheduling immediate test notification in 5 seconds');
      setTimeout(() => {
        this.debug('DEBUG: Test notification firing now');
        this.triggerReminder(reminder);
      }, 5000);
      return id;
    }

    this.setReminderTimeout(reminder);
    this.reminders.set(id, reminder);
    this.saveRemindersToStorage();

    this.debug(`Successfully scheduled reminder`, {
      id,
      scheduledCount: this.reminders.size
    });

    return id;
  }

  cancelReminder(id: string): void {
    const reminder = this.reminders.get(id);
    if (reminder) {
      clearTimeout(reminder.timeoutId);
      this.reminders.delete(id);
      this.saveRemindersToStorage();
      this.debug(`Cancelled reminder: ${reminder.eventTitle}`);
    }
  }

  cancelEventReminders(eventId: string): void {
    const reminderIds = Array.from(this.reminders.keys())
      .filter(id => this.reminders.get(id)?.eventId === eventId);
    
    reminderIds.forEach(id => this.cancelReminder(id));
    this.debug(`Cancelled all reminders for event: ${eventId}`);
  }

  updateEventReminder(
    eventId: string,
    eventTitle: string,
    eventDateTime: string,
    reminderTime: string,
    isActive: boolean
  ): void {
    this.cancelEventReminders(eventId);
    
    if (isActive && reminderTime) {
      this.scheduleReminder(eventId, eventTitle, eventDateTime, reminderTime);
    }
    this.debug(`Updated event reminder for: ${eventTitle}`, { isActive, reminderTime });
  }

  getScheduledReminders(): ScheduledReminder[] {
    return Array.from(this.reminders.values());
  }

  private setReminderTimeout(reminder: ScheduledReminder): void {
    const delay = reminder.triggerAt - Date.now();
    
    this.debug(`Setting timeout for reminder`, {
      title: reminder.eventTitle,
      delay: delay,
      delayMinutes: Math.round(delay / (1000 * 60)),
      triggerAt: new Date(reminder.triggerAt).toLocaleString()
    });

    if (delay <= 0) {
      this.debug('Delay is <= 0, triggering immediately');
      this.triggerReminder(reminder);
      return;
    }

    const maxDelay = 2147483647; // ~24.8 days
    
    if (delay > maxDelay) {
      this.debug('Delay exceeds maximum, will re-schedule later');
      reminder.timeoutId = window.setTimeout(() => {
        this.debug('Re-scheduling long-delay reminder');
        this.setReminderTimeout(reminder);
      }, maxDelay);
    } else {
      reminder.timeoutId = window.setTimeout(() => {
        this.debug('Timeout fired, triggering reminder');
        this.triggerReminder(reminder);
      }, delay);
    }

    this.debug('Timeout set successfully', { timeoutId: reminder.timeoutId });
  }

  private triggerReminder(reminder: ScheduledReminder): void {
    this.debug('Triggering reminder', {
      title: reminder.eventTitle,
      scheduledFor: new Date(reminder.triggerAt).toLocaleString(),
      actualTime: new Date().toLocaleString()
    });

    // Test notification permission
    this.debug('Testing notification before showing', {
      permission: Notification.permission,
      supported: 'Notification' in window
    });

    const notification: ReminderNotification = {
      id: `notification_${reminder.id}_${Date.now()}`,
      title: `Reminder: ${reminder.eventTitle}`,
      message: `Your event "${reminder.eventTitle}" is coming up soon!`,
      timestamp: formatRelativeTime(Date.now()),
      read: false,
      type: 'reminder',
      eventId: reminder.eventId
    };

    // Always try to show browser notification first
    this.showBrowserNotification(notification);

    // Notify all subscribers
    this.debug('Notifying subscribers', { callbackCount: this.notificationCallbacks.size });
    this.notificationCallbacks.forEach(callback => {
      try {
        callback(notification);
      } catch (error) {
        this.debug('Error in notification callback:', error);
      }
    });

    // Remove the triggered reminder
    this.reminders.delete(reminder.id);
    this.saveRemindersToStorage();

    this.debug('Reminder processing complete');
  }

  private showBrowserNotification(notification: ReminderNotification): void {
    this.debug('Attempting to show browser notification', {
      permission: Notification.permission,
      supported: 'Notification' in window
    });

    if (!('Notification' in window)) {
      this.debug('Browser notifications not supported');
      return;
    }

    if (Notification.permission !== 'granted') {
      this.debug('No permission for notifications', { permission: Notification.permission });
      return;
    }

    try {
      const browserNotification = new Notification(notification.title, {
        body: notification.message,
        icon: '/fragment-logo.png', // Using a valid image from public folder
        tag: notification.eventId,
        requireInteraction: true, // Keep notification visible
        silent: false
      });

      this.debug('Browser notification created successfully');

      browserNotification.onclick = () => {
        this.debug('Notification clicked');
        window.focus();
        browserNotification.close();
      };

      browserNotification.onerror = (error) => {
        this.debug('Notification error:', error);
      };

      

    } catch (error) {
      this.debug('Failed to create notification:', error);
    }
  }

  private setupVisibilityListener(): void {
    document.addEventListener('visibilitychange', () => {
      this.debug('Page visibility changed', {
        hidden: document.hidden,
        visibilityState: document.visibilityState
      });
      
      if (!document.hidden) {
        this.debug('Page became visible, checking for missed reminders');
        this.checkMissedReminders();
      }
    });
  }

  private checkMissedReminders(): void {
    const now = Date.now();
    const missedReminders = Array.from(this.reminders.values())
      .filter(reminder => reminder.triggerAt <= now);

    this.debug(`Found ${missedReminders.length} missed reminders`);
    missedReminders.forEach(reminder => this.triggerReminder(reminder));
  }

  private scheduleExistingReminders(): void {
    this.debug('Scheduling existing reminders from storage');
    this.reminders.forEach(reminder => {
      if (reminder.triggerAt > Date.now()) {
        this.setReminderTimeout(reminder);
      } else {
        this.debug('Existing reminder is in the past, triggering immediately', { title: reminder.eventTitle });
        this.triggerReminder(reminder);
      }
    });
  }

  private saveRemindersToStorage(): void {
    const remindersArray = Array.from(this.reminders.values()).map(reminder => ({
      ...reminder,
      timeoutId: undefined // Don't save timeout IDs
    }));
    localStorage.setItem('scheduledReminders', JSON.stringify(remindersArray));
    this.debug('Reminders saved to storage', { count: remindersArray.length });
  }

  private loadRemindersFromStorage(): void {
    try {
      const stored = localStorage.getItem('scheduledReminders');
      if (stored) {
        const remindersArray: ScheduledReminder[] = JSON.parse(stored);
        remindersArray.forEach(reminder => {
          this.reminders.set(reminder.id, reminder);
        });
        this.debug('Reminders loaded from storage', { count: remindersArray.length });
      } else {
        this.debug('No reminders found in storage');
      }
    } catch (error) {
      console.error('Failed to load reminders from storage:', error);
      this.debug('Error loading reminders:', error);
    }
  }
}

// Create singleton instance
export const reminderScheduler = new ReminderScheduler();