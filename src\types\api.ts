import { FollowerT, ZoneInfo } from ".";

export enum StatusCodes {
  OK = 200,
  CREATED = 201,
  DELETED = 204,
  RESENT = 302,
  EXISTS = 304,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  UNVERIFIED = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  UNPROCESSABLE_ENTITY = 422,
  SUSPENDED = 423,
  SERVER_ERROR = 500,
  UNAVAILABLE = 503,
}

export type ApiResult<T> =
  | {
      success: true;
      apiResponse: T;
      message: string;
      status: StatusCodes;
    }
  | { success: false; apiResponse: null; message: string; status: StatusCodes };

export type ApiResponse<T> =
  | {
      success: true;
      data: T;
      message: string;
      status: StatusCodes;
    }
  | { success: false; data: null; message: string; status: StatusCodes };

export type ApiErrorResponse = {
  data: null;
  success: false;
  message: string;
  status: StatusCodes;
};

export type LoginResponseT =
  | {
      existingUser: boolean;
      userData: {
        _id: string;
        uid: string;
      };
      accData: [];
      calendarData: [];
      agendaData: [];
    }
  | {
      _id: string;
      uid: string;
    };

export type CreatEventFormT = {
  _id: string;
  event: boolean;
  note: boolean;
  userType: string;
  usrId: string;
  calId: string;
  title: string;
  start: Date; // ISO string
  end: Date; // ISO string
  zone: ZoneInfo;
  deleted: false;
  blocked: false;
  profileImage: File | null;
  // not required
  calendarName?: string;
  allDayEvent?: boolean;
  repeat?: boolean;
  frequency?: string | null;
  repeatEvery?: string | number | null;
  days?: string[]; // e.g. ["Wednesday"]
  repeatEndDate?: Date;
  location?: string;
  link?: string[];
  picture?: string;
  youtube?: string;
  attach?: string[];
  description?: string;
  reminderSelected?: boolean;
  reminder?: string | null;
  personalReminder?: string;
  notifyBefore?: string;
};

export type CreatCalendarFormT = {
  _id: string;
  businessName: string;
  businessId: string;
  bizId: string;
  calendarName: string;
  calendarId: string;
  phoneNumber: string;
  email: string;
  deleted: boolean;
  followers: FollowerT[];
  businessUser: true;
  private: boolean;
  blocked: boolean;
  catagory: string;
  subCatagory: string;
  usrId: string;
  picture?: string | null;
  pictureImage: File | null;
  description: string;
};

export type CreateToDoFormT = {
  _id: string;
  taskId: string;
  category: string;
  priorityImg: string;
  task: string;
  date: string;
};

export type UpadteStatusFormT = {
  _id: string;
  taskId: string;
  status: string;
};

export type DeletTodoFormT = {
  _id: string;
  taskId: string;
};

export type CreateCommentFormT = {
  usrId: string;
  displayName: string;
  agendaId: string;
  Itemstart: string;
  commentId: string;
  comment: string;
  picture: string | null;
};

export type UpdateCommentFormT = {
  agendaId: string;
  commentId: string;
  comment: string;
};

export type DeleteCommentFormT = {
  agendaId: string;
  commentId: string;
};

export type ReportCommentFormT = {
  agendaId: string;
  commentId: string;
  usrId: string;
  reason: string;
};

export type CreateSubCommentFormT = CreateCommentFormT & {
  replyId: string;
  reply: string;
};

export type DeleteSubCommentFormT = {
  agendaId: string;
  replyId: string;
};

export type ReportSubCommentFormT = {
  agendaId: string;
  replyId: string;
  usrId: string;
  reason: string;
};

export type UpdateEventFormT = CreatEventFormT;

export type NotificationModelT = {
  _id: string;
  type?: string;
  isRead?: boolean;
  usrId?: string;
  displayName?: string;
  calId?: string;
  date?: string;
  calendarName?: string;
  bizUserId?: string;
  agendaId?: string;
  rsvpStatus?: string;
  email?: string;
  userId?: string;
  picture?: string;
};

export type NotificationResponseT = {
  notifications: NotificationModelT[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalNotifications: number;
    limit: number;
  };
};

export type NotificationFormT = {
  bizUserId?: string | null;
  page: number;
  limit: number;
  sortBy: "date";
  sortOrder: "desc" | "asc";
};

export type NotificatinMarkAsReadFormT = {
  bizUserId: string | null;
  notificationIds: string[];
};
