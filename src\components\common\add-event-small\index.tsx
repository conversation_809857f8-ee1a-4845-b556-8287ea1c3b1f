import { useState } from "react";

import AddEventEnterSmallView from "./enter-event";
import AddEventUploadSmallView from "./upload-event";

export default function AddEventSmallView({
  onCloseMainView,
}: {
  onCloseMainView: () => void;
}) {
  const [isUploadView, setIsUploadView] = useState(false);

  return (
    <div className="h-full max-h-[calc(100dvh-64px)] lg:max-h-[calc(100dvh-80px)] flex overflow-y-auto">
      {isUploadView ? (
        <AddEventUploadSmallView onClose={onCloseMainView} />
      ) : (
        <AddEventEnterSmallView
          onClose={onCloseMainView}
          openUploadView={() => {
            setIsUploadView(true);
          }}
        />
      )}
    </div>
  );
}
