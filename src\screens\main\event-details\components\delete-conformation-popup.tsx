import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

export function DeleteConformationPopup({
  isOpen,
  title,
  subTitle,
  onConfirm,
  onCancel,
}: {
  isOpen: boolean;
  title: string;
  subTitle: string;
  onConfirm: () => void;
  onCancel: () => void;
}) {
  return (
    <Dialog open={isOpen} onOpenChange={onCancel}>
      <DialogContent className="sm:max-w-[500px] min-h-60 !rounded-xl">
        <DialogHeader>
          <DialogTitle></DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
        <div className="flex flex-col gap-3 py-4 text-lg lg:text-2xl leading-6">
          <p className="text-center font-semibold">{title}</p>
          <p className="text-center  font-semibold text-fb-neutral-800">
            {subTitle}
          </p>
        </div>
        <DialogFooter className="!justify-center flex w-full items-center">
          <Button
            variant="default"
            className={cn(
              "w-40 rounded-lg bg-fb-success-600 text-white hover:bg-lime-700 drop-shadow-buttonShadow h-9"
            )}
            onClick={onConfirm}
          >
            Confirm
          </Button>
          <Button
            variant="destructive"
            className={cn(
              "w-40 rounded-lg  hover:bg-red-700 drop-shadow-buttonShadow h-9"
            )}
            onClick={onCancel}
          >
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}