import { Link, useParams } from "react-router-dom";
import { CalendarModelT } from "@/types";
import { Image } from "@/components/ui/image";
import BgImage from "@/assets/images/bg-image.png";
import { useGetAllCalendar } from "@/api/calendar";
import { useGetAccounDetails } from "@/api/account";
import CalendarNoneImg from "@/assets/images/calendar-none.png";

import AppLoader from "@/components/common/app-loader";
import { Button } from "@/components/ui/button";
import Fragmentlogo from "@/assets/images/fragment-EU-logo.png";
import { WEBSITE_URL } from "@/envs";

export default function AllBusinessCalendarsScreen() {
  const { userId } = useParams<{ userId: string }>();
  
  const { data, isLoading: isCalendarsLoading } = useGetAllCalendar({ usrId: userId });
  
  // Get account data for the user
  const {
    data: accountResponse,
    isLoading: isAccountLoading,
    // error: accountError,
  } = useGetAccounDetails({
    userId: userId,
  });

  // Extract the first account from the response
  const accountData = accountResponse?.accData?.[0];
  
  const calendarData = (data || []).filter(calendar => !calendar.private);
  
  if (isCalendarsLoading || isAccountLoading) {
    return <AppLoader />;
  }

  return (
    <div 
      className="min-h-screen bg-cover bg-center bg-no-repeat relative"
      style={{ backgroundImage: `url(${BgImage})` }}
    >
      {/* Background overlay */}
      <div className="absolute inset-0 bg-black/30"></div>
      
      {/* Content */}
      <div className="relative z-10">
        {/* Header */}
        <div className="bg-white/95 backdrop-blur-sm border-b px-4 sm:px-6 py-3 sm:py-4 flex justify-between items-center">
          <div className="flex items-center gap-2 sm:gap-3">
            <div className="flex items-center gap-1">
              <Image 
                src={Fragmentlogo}
                alt="Fragment Logo" 
                className="w-8 h-8 sm:w-10 sm:h-10" 
              />
            </div>
            <span className="text-xs sm:text-sm text-gray-500 ml-1 sm:ml-2">Powered by</span>
            <span className="font-semibold text-gray-900 text-sm sm:text-base">Fragment</span>
          </div>
          <Button 
            onClick={() => window.open(WEBSITE_URL, "_blank")}
            variant="ghost"
            className="text-xs sm:text-sm font-medium text-gray-900 hover:text-gray-700 px-2 sm:px-4"
          >
            Visit Website
          </Button>
        </div>

        {/* Centered Calendar Title - Mobile Responsive */}
        {accountData && (
          <div className="flex items-center justify-center py-6 sm:py-8 lg:py-12 px-4 sm:px-6">
            <div className="flex flex-col sm:flex-row items-center gap-4 sm:gap-6 lg:gap-8 max-w-4xl mx-auto text-center sm:text-left">
              {/* Account Picture */}
              <div className="w-20 h-20 sm:w-24 sm:h-24 lg:w-32 lg:h-32 rounded-xl sm:rounded-2xl overflow-hidden border-2 sm:border-4 border-white shadow-xl flex-shrink-0">
                <Image
                  src={accountData.picture ?? undefined}
                  alt="Account Avatar"
                  className="w-full h-full object-cover"
                />
              </div>
              
              {/* Account Name and Business ID */}
              <div className="flex flex-col">
                <h1 className="text-2xl sm:text-3xl lg:text-5xl font-bold text-white mb-1 sm:mb-2 drop-shadow-lg">
                  {accountData.businessName}
                </h1>
                <p className="text-sm sm:text-lg lg:text-xl text-white/80 mb-2 sm:mb-4 lg:mb-6 drop-shadow-md">
                  @{accountData.businessId}
                </p>
                <Button 
                  variant="outline" 
                  className="bg-white/90 text-gray-900 border-white hover:bg-white text-xs sm:text-sm px-3 py-1 sm:px-4 sm:py-2 rounded-full font-medium"
                >
                  View All Calendars
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Fallback title if no account data */}
        {!accountData && (
          <div className="flex flex-col items-center justify-center py-8 sm:py-12 lg:py-16 px-4 sm:px-6">
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-4 sm:mb-6 text-center drop-shadow-lg">
              All Public Calendars
            </h1>
          </div>
        )}

        {/* Main Content Container */}
        <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 pb-4 sm:pb-6">
          {/* Dark background container */}
          <div className="bg-slate-700/95 backdrop-blur-sm rounded-2xl sm:rounded-3xl shadow-2xl overflow-hidden">
            {calendarData.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8">
                <div className="w-48 h-48 sm:w-56 sm:h-56 lg:w-64 lg:h-64 flex flex-col bg-white shadow-lg drop-shadow-xl justify-center items-center gap-3 sm:gap-4 p-6 sm:p-8 rounded-2xl sm:rounded-3xl mb-6 sm:mb-8">
                  <img src={CalendarNoneImg} alt="" className="w-32 h-32 sm:w-36 sm:h-36 lg:w-40 lg:h-40" />
                </div>
                <p className="text-center font-semibold text-lg sm:text-xl lg:text-2xl xl:text-3xl leading-6 sm:leading-7 lg:leading-8 text-white max-w-2xl px-4">
                  This business does not have any public calendars yet.
                </p>
              </div>
            ) : (
              <div className="p-4 sm:p-6 lg:p-8">
                {/* Title text above calendars */}
                <h2 className="text-lg sm:text-xl lg:text-2xl font-semibold text-white text-center mb-4 sm:mb-6 lg:mb-8 px-2">
                  Check out other calendars by the same creator!
                </h2>
                
                <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
                  {calendarData.map((row, ind) => {
                    return <CalendarView data={row} key={ind} />;
                  })}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

const CalendarView = ({ data }: { data: CalendarModelT }) => {
  const { _id, calendarName, calendarId, picture } = data;
  return (
    <div className="bg-white/95 backdrop-blur-sm rounded-xl sm:rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 hover:scale-105">
      <div className="p-4 sm:p-5 lg:p-6 flex flex-col">
        {/* Calendar Avatar */}
        <div className="w-16 h-16 sm:w-18 sm:h-18 lg:w-20 lg:h-20 mb-3 sm:mb-4 rounded-full overflow-hidden border-2 border-gray-200 shadow-md mx-auto">
          <Image 
            src={picture || ""} 
            alt={calendarName}
            className="w-full h-full object-cover"
          />
        </div>
        
        {/* Calendar Info */}
        <Link to={`/calendar/public/${_id}`} className="text-center mb-3 sm:mb-4 group">
          <h3 className="text-base sm:text-lg font-bold text-gray-900 mb-1 sm:mb-2 group-hover:text-cyan-600 transition-colors duration-200 line-clamp-2" title={calendarName}>
            {calendarName}
          </h3>
          <p className="text-xs sm:text-sm text-gray-500 font-medium mb-2 sm:mb-3 lg:mb-4" title={calendarId}>
            @{calendarId}
          </p>
        </Link>
        
        {/* Descriptions section */}
        <div className="flex-1">
          <h4 className="text-xs sm:text-sm font-semibold text-gray-900 mb-1 sm:mb-2">Descriptions</h4>
          <p className="text-xs text-gray-600 leading-relaxed line-clamp-3">
            Some description about the calendar the calendar
          </p>
        </div>
      </div>
    </div>
  );
};