import { toast } from "sonner";
import { FormEvent, useState } from "react";
import { Link, Navigate, useNavigate } from "react-router";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import GmailPng from "@/assets/images/Gmail.png";
import ApplePng from "@/assets/images/apple.png";
import BgImage from "@/assets/images/bg-image.png";
import FacebookPng from "@/assets/images/facebook.png";
import BussinessLogo from "@/assets/images/fragment-business-logo.png";
import {
  useAppleLogin,
  useEmailSignUp,
  useFacebookLogin,
  useGoogleLogin,
  useSignUpAccount,
} from "@/api/auth";
import { useAuth } from "@/context/AuthContext";
import { User } from "firebase/auth";
import { extractFriendlyMessage } from "@/utils";

export default function SignUpScreen() {
  const [email, setEmail] = useState<string>("");
  const [password, setPassword] = useState<string>("");

  const { userId } = useAuth();

  const navigate = useNavigate();

  const emailSignUp = useEmailSignUp();
  const googleLogin = useGoogleLogin();
  const appleLogin = useAppleLogin();
  const facebookLogin = useFacebookLogin();
  const signUpAccount = useSignUpAccount();

  const handleSignUpFlow = async (signUpFn: () => Promise<User>) => {
    try {
      const user = await signUpFn();
      console.log("Signup result", user);

      const signUpResponse = await signUpAccount.mutateAsync(user);

      navigate("/signup-details", {
        state: {
          userId: signUpResponse.user._id,
          accoutData: signUpResponse.account,
        },
      });
    } catch (error) {
      let errorMessage = "Failed to sign up";

      if (error instanceof Error) {
        errorMessage = extractFriendlyMessage(error.message);
      }

      toast.error(errorMessage);
    }
  };

  const handleEmailSignUp = async (e: FormEvent) => {
    e.preventDefault();
    await handleSignUpFlow(() => emailSignUp.mutateAsync({ email, password }));
  };

  const handleGoogleLogin = async () => {
    await handleSignUpFlow(() => googleLogin.mutateAsync());
  };

  const handleAppleLogin = async () => {
    await handleSignUpFlow(() => appleLogin.mutateAsync());
  };

  const handleFacebookLogin = async () => {
    await handleSignUpFlow(() => facebookLogin.mutateAsync());
  };

  if (userId) {
    return <Navigate to="/" replace />;
  }

  return (
    <div className="bg-fb-bPrime-600 flex w-full h-dvh sm:h-screen relative overflow-hidden">
      <img
        src={BgImage}
        alt=""
        className="absolute z-0 w-full h-full opacity-20"
      />

      <div className="flex h-full px-4 py-6 w-1/2 z-10 ">
        <div className="bg-white flex w-full rounded-3xl px-6 sm:px-12 md:px-16 lg:px-24 justify-center items-center overflow-y-auto drop-shadow-cardOutShadow shadow-cardInnerShadow">
          <form
            className="flex flex-col gap-12 w-full justify-center"
            onSubmit={handleEmailSignUp}>
            <div className="border-b pb-1 border-black">
              <p className="text-3xl text-black">Sign Up using your Email Id</p>
            </div>
            <div className="flex flex-col gap-3 w-full   h-full">
              <div className="flex flex-col gap-1 w-full">
                <Label
                  htmlFor="email"
                  className="text-black font-semibold text-2xl">
                  Email Id*
                </Label>
                <Input
                  required
                  id="email"
                  value={email || ""}
                  type="email"
                  placeholder="<EMAIL>"
                  onChange={(e) => setEmail(e.target.value)}
                  className=""
                />
              </div>
              <div className="flex flex-col gap-1 w-full">
                <Label
                  htmlFor="password"
                  className="text-black font-semibold text-2xl">
                  Password*
                </Label>
                <Input
                  required
                  id="password"
                  value={password || ""}
                  type="password"
                  placeholder="********"
                  onChange={(e) => setPassword(e.target.value)}
                  className=""
                />
              </div>
            </div>
            <div className="flex flex-col gap-4 w-full">
              <div className="flex flex-col gap-1 w-full">
                <Button
                  className="text-xl font-semibold rounded-full text-white bg-fb-bPrime-600"
                  type="submit">
                  Sign Up
                </Button>
                <div className="flex items-center justify-between px-3">
                  <p className="text-black font-light ">
                    Already have an accunt?{" "}
                    <Link to={"/login"} className="">
                      Log_in
                    </Link>
                  </p>
                </div>
              </div>
              <div className="flex justify-center items-center gap-2 opacity-50">
                <div className="h-0.5 w-7 bg-black rounded-full mt-1" />
                or <div className="h-0.5 w-7 bg-black rounded-full mt-1" />
              </div>
              <div className="flex flex-col gap-2 w-full items-center">
                <p className="text-black">Sign up with</p>
                <div className="flex items-center flex-wrap justify-between gap-2 w-full">
                  <Button
                    type="button"
                    className="drop-shadow-buttonShadow font-semibold w-fit bg-fb-neutral-50 flex text-black  gap-1 hover:bg-neutral-100 rounded-full"
                    onClick={handleGoogleLogin}>
                    <img src={GmailPng} alt="gmail" className="p-1.5 h-10" />
                    Gmail
                  </Button>
                  <Button
                    type="button"
                    className="drop-shadow-buttonShadow font-semibold w-fit bg-fb-neutral-900 flex text-white hover:bg-neutral-800 rounded-full"
                    onClick={handleAppleLogin}>
                    <img src={ApplePng} alt="apple" className="p-1.5 h-10" />
                    Apple Id
                  </Button>
                  <Button
                    type="button"
                    className="drop-shadow-buttonShadow font-semibold w-fit bg-fb-neutral-50 flex text-black  gap-1 hover:bg-neutral-100 rounded-full"
                    onClick={handleFacebookLogin}>
                    <img
                      src={FacebookPng}
                      alt="facebook"
                      className="p-1.5 h-10"
                    />
                    Facebook
                  </Button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
      <div className="flex justify-center items-center w-1/2 z-10">
        <div className="flex flex-col">
          <div className="w-48 h-48">
            <img
              src={BussinessLogo}
              alt=""
              className="w-full h-full object-cover"
            />
          </div>
          <p className="text-3xl font-bold text-white">Connect with</p>
          <p className="text-[44px] leading-none font-bold text-white">
            Everyone
          </p>
        </div>
      </div>
    </div>
  );
}
