export default function AppLoader() {
  return (
    <div className="flex justify-center items-center ">
      {/* <div className="flex space-x-2">
          <div className="w-4 h-4 bg-red-500 rounded-full animate-bounce [animation-delay:-0.3s]" />
          <div className="w-4 h-4 bg-blue-500 rounded-full animate-bounce [animation-delay:-0.15s]" />
          <div className="w-4 h-4 bg-yellow-400 rounded-full animate-bounce" />
        </div> */}
      <div className="relative w-24 h-24">
        <div
          className="absolute w-5 h-5 bg-red-500 rounded-full animate-ping"
          style={{
            animationDelay: "0s",
            top: "0%",
            left: "50%",
            transform: "translate(-50%, -50%)",
          }}
        />
        <div
          className="absolute w-5 h-5 bg-yellow-400 rounded-full animate-ping"
          style={{
            animationDelay: "0.1s",
            top: "50%",
            left: "100%",
            transform: "translate(-50%, -50%)",
          }}
        />
        <div
          className="absolute w-5 h-5 bg-blue-500 rounded-full animate-ping"
          style={{
            animationDelay: "0.2s",
            top: "100%",
            left: "50%",
            transform: "translate(-50%, -50%)",
          }}
        />
        <div
          className="absolute w-5 h-5 bg-green-500 rounded-full animate-ping"
          style={{
            animationDelay: "0.3s",
            top: "50%",
            left: "0%",
            transform: "translate(-50%, -50%)",
          }}
        />
      </div>
    </div>
  );
}
