import { toast } from "sonner";
import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { format, isToday, isTomorrow, isValid } from "date-fns";
import { useNavigate } from "react-router-dom";
import {
  AlignJustify,
  ChevronLeft,
  Flame,
  Circle,
  X,
  Pencil,
} from "lucide-react";

import { cn } from "@/lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { TodoListModelT } from "@/types";
import { ToDoStatusValue } from "@/enums";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import BgImg from "@/assets/images/bg-image.png";
import BussinessInfo from "@/components/common/bussiness-info";
import UrgentImg from "@/assets/images/status-filter-urgent.png";
import SideCalendarView from "@/components/common/side-calendar-view";
import CompletedImg from "@/assets/images/status-filter-completed.png";
import NoImportantImg from "@/assets/images/status-filter-not-important.png";

import AddToDoTask from "./components/AddToDoTask";
import UpdateToDoTask from "./components/UpdateTodoTask";
import { Label } from "@/components/ui/label";
import { AlertPopup } from "@/components/common/alert-popup";
import { useGetAccounDetails } from "@/api/account";
import {
  useCategoryUpCre,
  useDeleteTodo,
  useUpdateTodoStatus,
} from "@/api/to-do";
import { DeletTodoFormT, UpadteStatusFormT } from "@/types/api";
import { useAuth } from "@/context/AuthContext";

const statuses = [
  { label: "Urgent", value: ToDoStatusValue.URGENT, image: UrgentImg },
  {
    label: "Not so Important",
    value: ToDoStatusValue.NO_IMPORTANT,
    image: NoImportantImg,
  },
  {
    label: "Completed",
    value: ToDoStatusValue.COMPLETED,
    image: CompletedImg,
  },
];

export default function ToDoListScreen() {
  const [filterData, setFilterData] = useState<TodoListModelT[]>([]);
  const CategoryMutate = useCategoryUpCre();
  const UpdateStatusMutate = useUpdateTodoStatus();
  const DeleteTodoMutate = useDeleteTodo();

  const { userId } = useAuth();
  const navigate = useNavigate();

  const { data } = useGetAccounDetails({ userId: userId });

  const [currentStatus, setCurrentStatus] = useState<ToDoStatusValue | null>(
    null
  );

  const [isUserSubScribe, setIsuSerSubscribe] = useState(true);

  const [isCategoresAdding, setCategoriesaAdding] = useState(false);
  const [cuurentCategoresList, setCurrentCategoresList] = useState<string[]>(
    []
  );

  const [updateTodo, setUpdateTodo] = useState<TodoListModelT | null>(null);
  const [addTaskKey, setAddTaskKey] = useState(0);

  const [categoryName, setCategoryName] = useState("");
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);

  const [currentCategory, setCurrentCategory] = useState<string | null>(null);

  const filterDataCallback = useCallback(
    (data: TodoListModelT[]) => {
      let filteredData = [...data];

      if (currentCategory) {
        filteredData = filteredData.filter(
          (row) => row.category === currentCategory
        );
      }

      if (currentStatus) {
        filteredData = filteredData.filter(
          (row) =>
            row.status === currentStatus || row.priorityImg === currentStatus
        );
      }

      if (currentStatus === ToDoStatusValue.COMPLETED) {
        filteredData = filteredData.filter(
          (row) => row.status === ToDoStatusValue.COMPLETED
        );
      } else {
        filteredData = filteredData.filter(
          (row) => row.status !== ToDoStatusValue.COMPLETED
        );
      }

      return filteredData;
    },
    [currentCategory, currentStatus] // Dependencies
  );

  useEffect(() => {
    setFilterData(filterDataCallback(data?.accData[0]?.Tasks || []));
    setCurrentCategoresList(data?.accData[0]?.Category || []);
  }, [data, filterDataCallback]);

  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState<string | null>(null);

  const handleRemoveCatg = (name: string) => {
    setCategoryToDelete(name);
    setShowConfirmDialog(true);
  };

  const confirmDeleteCategory = () => {
    if (!categoryToDelete) return;

    const AllCat = [...cuurentCategoresList];
    const removingIndex = AllCat.findIndex((cat) => cat === categoryToDelete);
    const filterCat = AllCat.filter((row) => row != categoryToDelete);

    // If we're editing the category being removed, cancel edit mode
    if (isEditMode && editingIndex === removingIndex) {
      handleCancelEdit();
    }
    // If we're editing a category after the one being removed, adjust the index
    else if (
      isEditMode &&
      editingIndex !== null &&
      editingIndex > removingIndex
    ) {
      setEditingIndex(editingIndex - 1);
    }

    if (data && data.accData[0] && data.accData[0]._id) {
      CategoryMutate.mutate(
        { accId: data.accData[0]._id, data: filterCat },
        {
          onSuccess() {
            toast.success("Category removed.");
            setShowConfirmDialog(false);
            setCategoryToDelete(null);
          },
          onError(error) {
            toast.error(error.message);
            setShowConfirmDialog(false);
            setCategoryToDelete(null);
          },
        }
      );
    } else {
      toast.error("Account Id not found...");
      setShowConfirmDialog(false);
      setCategoryToDelete(null);
    }
  };

  const handleAddCatg = () => {
    if (categoryName.trim() === "") {
      return;
    }

    let NewData: string[];

    if (isEditMode && editingIndex !== null) {
      // Update existing category
      NewData = [...cuurentCategoresList];
      NewData[editingIndex] = categoryName;
    } else {
      // Add new category
      NewData = [...cuurentCategoresList, categoryName];
    }

    if (data && data.accData[0] && data.accData[0]._id) {
      CategoryMutate.mutate(
        { accId: data.accData[0]._id, data: NewData },
        {
          onSuccess() {
            toast.success(isEditMode ? "Category updated." : "Category added.");
            setCurrentCategoresList(NewData); // Manually update the list
            setCategoryName("");
            setIsEditMode(false);
            setEditingIndex(null);
            setCategoriesaAdding(false); // Close the add/edit category section
          },
          onError(error) {
            toast.error(error.message);
          },
        }
      );
    } else {
      toast.error("Account Id not found...");
    }
  };

  const handleEditCategory = (categoryName: string, index: number) => {
    setCategoryName(categoryName);
    setIsEditMode(true);
    setEditingIndex(index);
  };

  const handleCancelEdit = () => {
    setCategoryName("");
    setIsEditMode(false);
    setEditingIndex(null);
  };

  const handleDeleteTodo = (taskId: string) => {
    if (data && data.accData[0] && data.accData[0]._id) {
      const daatas: DeletTodoFormT = {
        _id: data.accData[0]._id,
        taskId,
      };

      DeleteTodoMutate.mutate(daatas, {
        onSuccess() {
          toast.success("Todo Deleted...");
        },
        onError(error) {
          toast.error(error.message);
        },
      });
    } else {
      toast.error("Account Id not found...");
    }
  };

  const handleUpdateStatus = (taskId: string, status: string) => {
    if (data && data.accData[0] && data.accData[0]._id) {
      const daatas: UpadteStatusFormT = {
        _id: data.accData[0]._id,
        status,
        taskId,
      };

      UpdateStatusMutate.mutate(daatas, {
        onSuccess() {
          toast.success("Status Updated.");
        },
        onError(error) {
          toast.error(error.message);
        },
      });
    } else {
      toast.error("Account Id not found...");
    }
  };

  return (
    <div className="flex w-full h-dvh">
      <div className="flex flex-col gap-3 py-3 w-[calc(100%-250px)] h-full">
        {/* status */}
        <div
          className={cn(
            "mt-10 grid grid-cols-3 gap-1.5 lg:gap-3",
            isUserSubScribe ? "opacity-100" : "opacity-50"
          )}>
          {statuses.map((status) => (
            <button
              key={status.value}
              disabled={!isUserSubScribe}
              className={cn(
                "flex justify-center pb-8 items-center flex-col shadow-calenViewShadow drop-shadow-calenViewShadow p-2 lg:p-4 rounded-xl",
                currentStatus === status.value
                  ? "bg-fb-bPrime-hgts"
                  : "bg-white"
              )}
              onClick={() => setCurrentStatus(status.value)}>
              <img
                src={status.image}
                alt={status.label}
                className="h-20 lg:h-24"
              />
              <p className="text-sm lg:text-xl leading-4 -tracking-wide">
                {status.label}
              </p>
            </button>
          ))}
        </div>
        {/* todo list */}
        <div
          className={cn(
            "flex flex-col gap-2 lg:gap-4 p-3 lg:p-4 rounded-xl h-full w-full overflow-y-auto no-scrollbar",
            isUserSubScribe ? "bg-white" : "bg-white/50"
          )}>
          {currentStatus && (
            <Button
              variant={"ghost"}
              className="w-fit h-6 lg:h-8 "
              onClick={() => {
                setCurrentStatus(null);
              }}>
              <ChevronLeft /> Back
            </Button>
          )}
          {/* task title */}
          <div
            className={cn(
              "flex -tracking-wid text-lg md:text-xl lg:text-2xl leading-4",
              isUserSubScribe ? "opacity-100" : "opacity-50"
            )}>
            {!currentStatus ? (
              <p>Tasks to do</p>
            ) : currentStatus === ToDoStatusValue.COMPLETED ? (
              <p>These tasks are 'Completed'</p>
            ) : currentStatus === ToDoStatusValue.URGENT ? (
              <p>These tasks are 'Urgent'</p>
            ) : (
              <p>Complete these tasks 'soon'</p>
            )}
          </div>
          {/* category */}
          {isUserSubScribe && (
            <div className="flex px-2 gap-2 flex-wrap items-center">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    size={"sm"}
                    variant={"ghost"}
                    className={cn(
                      "!h-8 !w-8 drop-shadow-calenViewShadow shadow-calenViewShadow text-black !rounded-12px !font-normal",

                      isCategoresAdding
                        ? "bg-fb-bPrime-hgts hover:bg-fb-bPrime-hgts/90"
                        : "bg-fb-neutral-100 hover:bg-fb-neutral-200"
                    )}>
                    <AlignJustify className="!w-4 !h-9" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-fb-neutral-50 rounded-8px">
                  <DropdownMenuItem
                    className="text-fb-neutral-800 cursor-pointer"
                    onClick={() => {
                      setCategoriesaAdding(true);
                    }}>
                    Add Category
                  </DropdownMenuItem>
                  <DropdownMenuSeparator className="bg-[#B5B5B550]" />
                  <DropdownMenuItem
                    className="text-fb-neutral-800 cursor-pointer"
                    onClick={() => {
                      setCategoriesaAdding(true);
                    }}>
                    Edit Category
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              {cuurentCategoresList.map((cat, index) => {
                return (
                  <Button
                    key={index}
                    onClick={() => {
                      setCurrentCategory((old) => (old === cat ? null : cat));
                    }}
                    className={cn(
                      "!h-8 drop-shadow-calenViewShadow shadow-calenViewShadow text-black !rounded-12px !font-normal",
                      "text-xs lg:text-sm",
                      currentCategory === cat
                        ? "bg-fb-bPrime-hgts hover:bg-fb-bPrime-hgts/90"
                        : "bg-fb-neutral-100 hover:bg-fb-neutral-200"
                    )}>
                    {cat}
                  </Button>
                );
              })}
              {/* <Button
                onClick={() => {
                  setCategoriesaAdding((old) => !old);
                }}
                className={cn(
                  "!h-8 drop-shadow-calenViewShadow shadow-calenViewShadow text-black !rounded-12px !font-normal",
                  "text-xs lg:text-sm ml-auto",

                  isCategoresAdding
                    ? "bg-fb-bPrime-hgts hover:bg-fb-bPrime-hgts/90"
                    : "bg-fb-neutral-100 hover:bg-fb-neutral-200"
                )}>
                Edit Category
              </Button> */}
            </div>
          )}
          {/* list & subscription*/}
          <div className="relative">
            <div className="flex flex-col gap-2 pointer-events-auto p-4">
              {isUserSubScribe ? (
                filterData.map((row, ind) => {
                  return (
                    <ToDoListView
                      data={row}
                      key={ind}
                      onStatusUpdate={(status: string) =>
                        handleUpdateStatus(row.taskId, status)
                      }
                      onDelete={() => handleDeleteTodo(row.taskId)}
                      onUpdateTodod={() => setUpdateTodo(row)}
                      onAddAsEvent={(taskName: string) =>
                        navigate(`/add-event?name=${taskName}`)
                      }
                    />
                  );
                })
              ) : (
                <SubScriptionCard
                  subSctibe={() => {
                    setIsuSerSubscribe(true);
                  }}
                />
              )}
            </div>
            {/* Disabled overlay when not subscribed */}
            {isCategoresAdding && (
              <div className="absolute inset-0 bg-gray-500/50 z-10 pointer-events-none rounded-xl" />
            )}
          </div>
        </div>
      </div>
      {/* events sidebar data */}
      <div className="w-96 lg:w-[450px] px-4 py-3 flex flex-col gap-3  overflow-y-auto no-scrollbar">
        {/* bussiness details */}
        <BussinessInfo />
        {isCategoresAdding ? (
          <div className="py-3 flex flex-col w-full gap-4 h-full overflow-y-auto no-scrollbar ">
            <div className="flex items-center justify-between ">
              <p className="px-4 font-medium sm:text-base lg:text-lg">
                Edit Categories
              </p>
              <Button
                onClick={() => {
                  setCategoriesaAdding(false);
                }}
                variant={"ghost"}
                size={"icon"}
                className="!h7 !w7 rounded-full">
                <X />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {cuurentCategoresList.map((cat, index) => {
                return (
                  <div
                    key={index}
                    className={cn(
                      "!h-8 drop-shadow-calenViewShadow shadow-calenViewShadow text-black !rounded-12px !font-normal",
                      "text-xs lg:text-sm flex items-center gap-1 px-2",
                      isEditMode && editingIndex === index
                        ? "bg-fb-bPrime-100 border-2 border-fb-bPrime-500"
                        : "bg-fb-neutral-100 hover:bg-fb-neutral-200"
                    )}>
                    {cat}
                    <span
                      onClick={(e) => {
                        e.stopPropagation(); // This stops click propagation
                        handleEditCategory(cat, index);
                      }}
                      className={cn("hover:text-blue-500 cursor-pointer ml-1")}>
                      <Pencil size={14} />
                    </span>
                    <span
                      onClick={(e) => {
                        e.stopPropagation(); // This stops click propagation
                        handleRemoveCatg(cat);
                      }}
                      className={cn("hover:text-fb-warn-500 cursor-pointer")}>
                      <X size={16} />
                    </span>
                  </div>
                );
              })}
            </div>
            <div className={cn("flex flex-col mt-2 gap-1")}>
              <Label htmlFor="cat" className="text-fb-neutral-800">
                {isEditMode ? "Update Category" : "Add New Category"}
              </Label>
              <div className="flex gap-2">
                <Input
                  id="cat"
                  placeholder="Name"
                  value={categoryName}
                  onChange={(e) => setCategoryName(e.target.value)}
                  className="h-8 !rounded-12px border-fb-bPrime-400 !shadow-none !drop-shadow-none outline-none"
                />
                {isEditMode && (
                  <Button
                    type="button"
                    onClick={handleCancelEdit}
                    variant="outline"
                    size="sm"
                    className="h-8 !rounded-12px">
                    Cancel
                  </Button>
                )}
              </div>
            </div>
            <Button
              disabled={!categoryName}
              onClick={handleAddCatg}
              className=" !rounded-12px h-8 text-sm font-normal lg:text-base mt-auto sticky bottom-1">
              {isEditMode ? "Update Category" : "Add Category"}
            </Button>
          </div>
        ) : updateTodo ? (
          <UpdateToDoTask
            accId={data?.accData[0]?._id}
            categorys={cuurentCategoresList}
            oldData={updateTodo}
            onComplete={() => {
              setUpdateTodo(null);
            }}
          />
        ) : (
          <AddToDoTask
            isSubscribe={isUserSubScribe}
            accId={data?.accData[0]?._id}
            categorys={cuurentCategoresList}
            key={addTaskKey}
            onTaskAdded={() => setAddTaskKey(prev => prev + 1)}
          />
        )}
        {/* calendar view */}
        <SideCalendarView />
      </div>
      <AlertPopup
        isOpen={showConfirmDialog}
        title={`Are you sure you want to delete the category "${categoryToDelete}"?`}
        onCLose={() => setShowConfirmDialog(false)}
        onSuccess={confirmDeleteCategory}
      />
    </div>
  );
}

const formatDate = (date: Date | string) => {
  let newDate = new Date(date || "");

  if (!isValid(newDate)) {
    newDate = new Date(); // fallback to today
  }

  if (isToday(newDate)) return "Today";
  if (isTomorrow(newDate)) return "Tomorrow";
  return format(newDate, "dd/MM/yy"); // Format as 25/04/25
};

const ToDoListView = ({
  onStatusUpdate,
  data,
  onDelete,
  onUpdateTodod,
  onAddAsEvent,
}: {
  data: TodoListModelT;
  onStatusUpdate: (val: string) => void;
  onDelete: () => void;
  onUpdateTodod: () => void;
  onAddAsEvent: (taskName: string) => void;
}) => {
  const formatedDate = formatDate(data.date);

  return (
    <div className="flex pb-1 gap-4 border-b border-black/30 items-center">
      <button
        onClick={() =>
          onStatusUpdate(
            data.status === ToDoStatusValue.COMPLETED
              ? ToDoStatusValue.NO_IMPORTANT
              : ToDoStatusValue.COMPLETED
          )
        }>
        <Circle
          strokeWidth={1.3}
          className={cn(
            data.status === ToDoStatusValue.COMPLETED && "fill-fb-yellow-200 ",
            "size-4 lg:size-5"
          )}
        />
      </button>
      <div className="flex flex-col text-fb-neutral-800">
        <p className="font-medium text-sm lg:text-base !leading-4">
          {data.task}
        </p>
        {data.category && (
          <p className="text-xs lg:text-sm leading-3 text-fb-neutral-500">
            {data.category}
          </p>
        )}
        <p className="text-xs lg:text-sm leading-3">{data.comment}</p>
      </div>
      <div className="flex gap-2 items-center ml-auto">
        <p className="text-xs lg:text-sm font-semibold text-fb-neutral-600 tracking-wide">
          {formatedDate}
        </p>
        <Flame
          className={cn(
            "size-4 lg:size-5 fill-fb-option-7",
            data.priorityImg === ToDoStatusValue.URGENT && "fill-fb-option-4",
            data.priorityImg === ToDoStatusValue.NO_IMPORTANT &&
              "fill-fb-option-3"
          )}
          strokeWidth={1.3}
        />

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              size={"sm"}
              variant={"ghost"}
              className="!h-6 w-6 text-black hover:bg-fb-neutral-200">
              <AlignJustify className="!w-4 !h-9" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="bg-fb-neutral-50 rounded-8px">
            <DropdownMenuItem
              className="text-fb-neutral-800 cursor-pointer"
              onClick={onUpdateTodod}>
              Change infomation
            </DropdownMenuItem>
            <DropdownMenuSeparator className="bg-[#B5B5B550]" />
            <DropdownMenuItem
              className="text-fb-neutral-800 cursor-pointer"
              onClick={onDelete}>
              Delete
            </DropdownMenuItem>
            <DropdownMenuSeparator className="bg-[#B5B5B550]" />
            <DropdownMenuItem
              className="text-fb-neutral-800 cursor-pointer"
              onClick={() => onAddAsEvent(data.task)}>
              Add as an Event
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};

const SubScriptionCard = ({ subSctibe }: { subSctibe: () => void }) => {
  const LIST = ["To do lists", "xyz", "abc"];

  return (
    <div className="bg-white m-auto max-w-lg w-full rounded-12px flex flex-col overflow-hidden text-black">
      <div className=" bg-fb-bPrime-hgts flex w-full flex-col relative overflow-hidden">
        <img src={BgImg} alt="" className="w-full h-auto absolute opacity-70" />
        <div className="py-2 px-6 flex flex-col gap-2 mt-2  mb-1 z-10">
          <p className="text-base lg:text-lg leading-4 italic">Fragment Pro</p>
          <p className="font-semibold text-2xl lg:text-3xl leading-5">
            ₹50.60/-
          </p>
          <p className="font-semibold text-base lg:text-xl leading-4">
            {" "}
            Rupees/Month
          </p>
        </div>
      </div>
      <div className="bg-white flex w-full flex-col gap-2 py-3 lg:py-4 px-6">
        <div className="flex flex-col gap-2">
          <p className="font-semibold text-base lg:text-xl leading-4">
            The subscription will include
          </p>

          <div className="pl-4 lg:pl-6 text-sm lg:text-base leading-4">
            {LIST.map((li, ind) => (
              <li key={ind}>{li}</li>
            ))}
          </div>
        </div>

        <Button
          className="bg-fb-bPrime-600 font-semibold text-base lg:text-xl rounded-lg w-44 h-8 lg:w-60 mx-auto hover:bg-fb-bPrime-500 "
          onClick={subSctibe}>
          Subscribe
        </Button>
        <Button
          variant={"link"}
          className="w-fit mx-auto h-2 text-black/75 text-xxs lg:text-xs">
          T&C apply
        </Button>
      </div>
    </div>
  );
};
