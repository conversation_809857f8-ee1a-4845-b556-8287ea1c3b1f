import { toast } from "sonner";
import { FormEvent, useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router";
import { Trash2, X } from "lucide-react";

import { FileWithPreview } from "@/types";
import { Input } from "@/components/ui/input";
import { Image } from "@/components/ui/image";
import { Button } from "@/components/ui/button";
import AchiveIcon from "@/assets/svg/achive-icon.svg";
import PlaceholderImg from "@/assets/images/add-placeholder.jpg";
import BussinessInfo from "@/components/common/bussiness-info";
import { cn } from "@/lib/utils";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { DateTimePicker } from "@/components/common/date-time-picker";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useUpdateEvent, useGetEventById } from "@/api/event";
import { useEventWithReminders } from "@/hooks/useEventWithReminders";
import { useNotificationsWithReminders } from "@/hooks/useNotificationsWithReminders";
import { currentTimeZone, getZoneFromTzCode, generateUUID } from "@/utils";
import { UpdateEventFormT, CreatEventFormT } from "@/types/api";
import { rawEventFormToModel } from "@/utils/model";
import { ConformationPopup } from "@/components/common/conformation-popup";
import { useCalendarsWithEvents } from "@/hook/useCalendarsWithEvents";
import { useAuth } from "@/context/AuthContext";
import AppLoader from "@/components/common/app-loader";
import { useCustomDropzone, FILE_TYPES } from "@/hook/useCustomDropzone";
import { addHours, addMonths } from "date-fns";

const ReminderEventTime = [
  { name: "5 Mins", value: "5-min" },
  { name: "10 Mins", value: "10-min" },
  { name: "15 Mins", value: "15-min" },
  { name: "30 Mins", value: "30-min" },
  { name: "1 Hour", value: "1-hour" },
  { name: "1 Day", value: "1-day" },
];

const RepeatEventTime = [
  { name: "Never", value: "Never" },
  { name: "Daily", value: "Daily" },
  { name: "Weekly", value: "Weekly" },
  { name: "Monthly", value: "Monthly" },
];

const RepeatEveryTime = Array.from({ length: 30 }, (_, i) => ({
  name: `${i + 1}`,
  value: `${i + 1}`,
}));

export default function EditEventScreen() {
  const { eventId } = useParams();
  const { state } = useLocation();
  const { mutate: updateEvent, isPending } = useUpdateEvent();
  const { data: eventData, isLoading } = useGetEventById(eventId || "");
  const navigation = useNavigate();
  const { updateEventWithReminder, createEventWithReminder, isUpdating, isCreating } = useEventWithReminders();
  const { requestNotificationPermission } = useNotificationsWithReminders();

  const eventDate = state?.eventDate;

  const [showDeleteAlert, setShowDeleteAlert] = useState(false);
  const [showUpdateAlert, setShowUpdateAlert] = useState(false);

  const { userId } = useAuth();
  const { apiCalendars } = useCalendarsWithEvents(userId);

  const [eventImage, setEventImage] = useState<FileWithPreview[]>([]);
  const [eventName, setEventName] = useState("");
  const [isAllDayEvent, setIsAllDayEvent] = useState(false);
  const [chooseCalendar, setChooseCalendar] = useState<string[]>([]);
  const [reminderTime, setReminderTime] = useState<string | null>("15-min");
  const [isReminderActive, setIsReminderActive] = useState(true);
  const [repeatTime, setRepeatTime] = useState<string | null>(null);
  const [repeatEndDate, setRepeatEndDate] = useState<Date | null>(null);
  const [repeatEveryValue, setRepeatEveryValue] = useState<string>("1");
  const [attachedLinks, setAttachedLinks] = useState<string[]>([""]);
  const [startDate, setStartDate] = useState<Date>(new Date());
  const [endDate, setEndDate] = useState<Date>(() => {
    const now = new Date();
    return new Date(now.setHours(now.getHours() + 1));
  });
  const [location, setLocation] = useState("");
  const [description, setDescription] = useState("");

  const [previewImage, setPreviewImage] = useState<string | null>(null);

  const [conformationPopup, setConformationPopup] = useState({
    open: false,
    title: "",
    subTitle: "",
  });

  // Load event data when available
  useEffect(() => {
    if (eventData) {
      setEventName(eventData.title || "");
      setIsAllDayEvent(eventData.allDayEvent || false);
      setChooseCalendar(eventData.calId ? [eventData.calId] : []);
      setReminderTime(eventData.reminder || null);
      setIsReminderActive(!!eventData.reminder);
      setRepeatTime(eventData.frequency || null);
      setStartDate(new Date(eventData.start || new Date()));
      setEndDate(new Date(eventData.end || new Date()));
      setLocation(eventData.location || "");
      setDescription(eventData.description || "");
      setAttachedLinks(eventData.link || [""]);
      setPreviewImage(eventData.picture || "");
      setRepeatEndDate(eventData.repeatEndDate ? new Date(eventData.repeatEndDate) : null);
      setRepeatEveryValue(eventData.repeatEvery ? String(eventData.repeatEvery) : "1");
    }
  }, [eventData]);

  const handleLinkChange = (index: number, value: string) => {
    const newLinks = [...attachedLinks];
    newLinks[index] = value;
    setAttachedLinks(newLinks);
  };

  const handleLinkRemove = (index: number) => {
    const updated = attachedLinks.filter((_, i) => i !== index);
    setAttachedLinks(updated);
  };

  const handleAddLink = () => {
    setAttachedLinks([...attachedLinks, ""]);
  };

  const { getRootProps, getInputProps } = useCustomDropzone({
    acceptedFileTypes: FILE_TYPES.IMAGES,
    maxFiles: 1,
    maxSize: 1 * 1024 * 1024, // 1MB
    multiple: false,
    showToastOnError: true,
    customErrorMessages: {
      "file-invalid-type": "Please upload only image files for event cover",
      "file-too-large": "Event image is too large",
      "too-many-files": "You can only upload one event image",
    },
    onFilesAccepted: (acceptedFiles) => {
      setEventImage(
        acceptedFiles.map((file) =>
          Object.assign(file, {
            preview: URL.createObjectURL(file),
          })
        )
      );
    },
  });

  const handleUpdateEvent = async (e: FormEvent) => {
    e.preventDefault();
    if (eventData?.repeat) {
      setShowUpdateAlert(true);
    } else {
      await processUpdateEvent();
    }
  };

    const processUpdateEvent = async (updateOption?: "this" | "future" | "all") => {
    if (!chooseCalendar.length || !apiCalendars || !eventId || !eventData) {
      toast.info(
        "At least one calendar is required and event data must be loaded."
      );
      return;
    }
    if ((updateOption === 'this' || updateOption === 'future') && !eventDate) {
        toast.error("Cannot update recurring event without a specific event date.");
        return;
    }

    setShowUpdateAlert(false);

    const processedLinks = attachedLinks
      .map((link) => {
        const trimmedLink = link.trim();
        if (trimmedLink && !trimmedLink.startsWith("http://") && !trimmedLink.startsWith("https://")) {
          return `https://${trimmedLink}`;
        }
        return trimmedLink;
      })
      .filter((link) => link);

    let successfulCreations = 0;
    let successfulUpdates = 0;
    let failed = 0;

    if (updateOption === "this" && eventDate) {
      // 1. Add current event date to restrict array of the original event
      const updatedData = {
        ...eventData,
        restrict: [...(eventData.restrict || []), eventDate],
      };
      await updateEvent(
        { _id: eventData._id, restrict: updatedData.restrict }
      );
      successfulUpdates++;

      const newSingleEventStart = new Date(eventDate);
      newSingleEventStart.setHours(startDate.getHours(), startDate.getMinutes(), startDate.getSeconds());

      const duration = endDate.getTime() - startDate.getTime();
      const newSingleEventEnd = new Date(newSingleEventStart.getTime() + duration);

      // 2. Create a new single event in the chosen calendars
      for (const calendarId of chooseCalendar) {
        try {
          const calendar = apiCalendars.find((cal) => cal._id === calendarId);
          if (!calendar) {
            failed++;
            continue;
          }
          const formData: CreatEventFormT = {
            _id: generateUUID("bizAgenda"),
            calId: calendarId,
            calendarName: calendar.calendarName || "",
            end: newSingleEventEnd,
            start: newSingleEventStart,
            event: true,
            note: false,
            title: eventName,
            userType: "businessUser",
            usrId: userId || "",
            zone: getZoneFromTzCode(currentTimeZone),
            allDayEvent: isAllDayEvent,
            blocked: false,
            deleted: false,
            location,
            description,
            reminder: isReminderActive ? reminderTime : null,
            reminderSelected: isReminderActive,
            frequency: undefined, // Not recurring
            days: [],
            repeat: false, // Not recurring
            repeatEvery: undefined,
            repeatEndDate: undefined,
            link: processedLinks,
            profileImage: eventImage[0] || null,
            picture: undefined,
          };
          const newData = rawEventFormToModel(formData);
          await createEventWithReminder({
            ...newData,
            dateTime: newSingleEventStart.toISOString(),
            reminderSelected: isReminderActive,
            reminder: isReminderActive ? reminderTime : null,
          });
          successfulCreations++;
        } catch (error) {
          console.error(`Failed to create event in calendar ${calendarId}:`, error);
          failed++;
        }
      }
    } else if (updateOption === "future" && eventDate) {
      // 1. Update repeat end date of original event
      const newEndDate = new Date(eventDate);
      newEndDate.setDate(newEndDate.getDate() - 1);
      await updateEvent(
        { _id: eventData._id, repeatEndDate: newEndDate.toISOString() }
      );
      successfulUpdates++;

      // 2. Create a new recurring event in the chosen calendars
      const newSeriesStartDate = new Date(eventDate);
      newSeriesStartDate.setHours(startDate.getHours(), startDate.getMinutes(), startDate.getSeconds());
      const duration = endDate.getTime() - startDate.getTime();
      const newSeriesEndDate = new Date(newSeriesStartDate.getTime() + duration);

      for (const calendarId of chooseCalendar) {
        try {
          const calendar = apiCalendars.find((cal) => cal._id === calendarId);
          if (!calendar) {
            failed++;
            continue;
          }
          const formData: CreatEventFormT = {
            _id: generateUUID("bizAgenda"),
            calId: calendarId,
            calendarName: calendar.calendarName || "",
            end: newSeriesEndDate,
            start: newSeriesStartDate,
            event: true,
            note: false,
            title: eventName,
            userType: "businessUser",
            usrId: userId || "",
            zone: getZoneFromTzCode(currentTimeZone),
            allDayEvent: isAllDayEvent,
            blocked: false,
            deleted: false,
            location,
            description,
            reminder: isReminderActive ? reminderTime : null,
            reminderSelected: isReminderActive,
            frequency: repeatTime === "Never" ? undefined : repeatTime,
            days: [],
            repeat: repeatTime !== "Never" && repeatTime !== null,
            repeatEvery:
              repeatTime === "Never" ? undefined : parseInt(repeatEveryValue),
            repeatEndDate: eventData.repeatEndDate ? new Date(eventData.repeatEndDate) : undefined,
            link: processedLinks,
            profileImage: eventImage[0] || null,
            picture: undefined,
          };
          const newData = rawEventFormToModel(formData);
          await createEventWithReminder({
            ...newData,
            dateTime: newSeriesStartDate.toISOString(),
            reminderSelected: isReminderActive,
            reminder: isReminderActive ? reminderTime : null,
          });
          successfulCreations++;
        } catch (error) {
          console.error(`Failed to create event in calendar ${calendarId}:`, error);
          failed++;
        }
      }
    } else {
      // "all" or non-recurring event
      const originalCalendarId = eventData.calId;
      const calendarsToCreate = chooseCalendar.filter(
        (id) => id !== originalCalendarId
      );
      const calendarToUpdate = chooseCalendar.find(
        (id) => id === originalCalendarId
      );

      if (calendarToUpdate) {
        try {
          const calendar = apiCalendars.find(
            (cal) => cal._id === calendarToUpdate
          );
          const formData: UpdateEventFormT = {
            _id: eventId,
            calId: calendarToUpdate,
            calendarName: calendar?.calendarName || "",
            end: endDate,
            start: startDate,
            event: true,
            note: false,
            title: eventName,
            userType: "businessUser",
            usrId: userId || "",
            zone: getZoneFromTzCode(currentTimeZone),
            allDayEvent: isAllDayEvent,
            blocked: false,
            deleted: false,
            location,
            description,
            reminder: isReminderActive ? reminderTime : null,
            reminderSelected: isReminderActive,
            frequency: repeatTime === "Never" ? undefined : repeatTime,
            days: repeatTime && repeatTime !== "Never" ? [repeatTime] : [],
            repeat: repeatTime !== "Never" && repeatTime !== null,
            repeatEndDate:
              repeatTime === "Never" || repeatEndDate === null
                ? undefined
                : repeatEndDate,
            repeatEvery:
              repeatTime === "Never" ? undefined : parseInt(repeatEveryValue),
            picture:
              eventImage.length > 0
                ? eventData?.picture || ""
                : previewImage || undefined,
            link: processedLinks,
            profileImage: eventImage[0] || null,
          };

          const updateData = rawEventFormToModel(formData);
          await updateEventWithReminder(eventId, {
            ...updateData,
            dateTime: startDate.toISOString(),
            reminderSelected: isReminderActive,
            reminder: isReminderActive ? reminderTime : null,
          });
          successfulUpdates++;
        } catch (error) {
          console.error(
            `Failed to update event in calendar ${calendarToUpdate}:`,
            error
          );
          failed++;
        }
      } else {
        // If original calendar is not selected, delete the original event
        try {
          await updateEvent({ _id: eventId, deleted: true });
        } catch (error) {
          console.error("Failed to delete original event:", error);
        }
      }

      // Create new events for additional calendars
      for (const calendarId of calendarsToCreate) {
        try {
          const calendar = apiCalendars.find((cal) => cal._id === calendarId);
          if (!calendar) {
            console.error(
              `Calendar with ID ${calendarId} not found. Skipping event creation.`
            );
            failed++;
            continue;
          }

          const formData: CreatEventFormT = {
            _id: generateUUID("bizAgenda"),
            calId: calendarId,
            calendarName: calendar.calendarName || "",
            end: endDate,
            start: startDate,
            event: true,
            note: false,
            title: eventName,
            userType: "businessUser",
            usrId: userId || "",
            zone: getZoneFromTzCode(currentTimeZone),
            allDayEvent: isAllDayEvent,
            blocked: false,
            deleted: false,
            location,
            description,
            reminder: isReminderActive ? reminderTime : null,
            reminderSelected: isReminderActive,
            frequency: repeatTime === "Never" ? undefined : repeatTime,
            days: [],
            repeat: repeatTime !== "Never" && repeatTime !== null,
            repeatEvery:
              repeatTime === "Never" ? undefined : parseInt(repeatEveryValue),
            repeatEndDate:
              repeatTime === "Never" || repeatEndDate === null
                ? undefined
                : repeatEndDate,
            link: processedLinks,
            profileImage: eventImage[0] || null,
            picture: undefined,
          };

          const newData = rawEventFormToModel(formData);
          await createEventWithReminder({
            ...newData,
            dateTime: startDate.toISOString(),
            reminderSelected: isReminderActive,
            reminder: isReminderActive ? reminderTime : null,
          });
          successfulCreations++;
        } catch (error) {
          console.error(
            `Failed to create event in calendar ${calendarId}:`,
            error
          );
          failed++;
        }
      }
    }

    const totalSuccess = successfulCreations + successfulUpdates;
    if (totalSuccess > 0) {
      let title = "";
      if (successfulUpdates > 0) {
        title += `Event updated in ${successfulUpdates} calendar${
          successfulUpdates > 1 ? "s" : ""
        }. `;
      }
      if (successfulCreations > 0) {
        title += `Event created in ${successfulCreations} calendar${
          successfulCreations > 1 ? "s" : ""
        }.`;
      }
      if (failed > 0) {
        title += ` ${failed} failed.`;
      }
      handleSetConformation({
        title: title.trim(),
        subTitle: "",
      });
    } else {
      toast.error("Failed to update or create event in any calendar");
    }
  };

  const handleDeleteEvent = () => {
    setShowDeleteAlert(true);
  };

  const thisEventOnly = () => {
    if (!eventData?._id || !eventDate) return;

    // Add current event date to restrict array
    const updatedData = {
      ...eventData,
      restrict: [...(eventData.restrict || []), eventDate],
    };

    updateEvent(
      { _id: eventData._id, restrict: updatedData.restrict },
      {
        onSuccess() {
          toast.success("Event removed from this date");
          navigation("/");
          setShowDeleteAlert(false);
        },
        onError(error: Error) {
          toast.error(error.message);
          setShowDeleteAlert(false);
        },
      }
    );
  };

  const thisAndFutureEvents = () => {
    if (!eventData?._id || !eventDate) return;

    // Set repeat end date to one day before current event
    const endDate = new Date(eventDate);
    endDate.setDate(endDate.getDate() - 1);

    updateEvent(
      { _id: eventData._id, repeatEndDate: endDate.toISOString() },
      {
        onSuccess() {
          toast.success("This and future events deleted");
          navigation("/");
          setShowDeleteAlert(false);
        },
        onError(error: Error) {
          toast.error(error.message);
          setShowDeleteAlert(false);
        },
      }
    );
  };

  const handleDelete = () => {
    updateEvent(
      { _id: eventData?._id || "", deleted: true },
      {
        onSuccess() {
          toast.success("Event deleted successfully");
          navigation("/");
          setShowDeleteAlert(false);
        },
        onError(error: Error) {
          toast.error(error.message);
          setShowDeleteAlert(false);
        },
      }
    );
  };

  const handleSetConformation = ({
    title,
    subTitle,
  }: {
    title: string;
    subTitle: string;
  }) => {
    setConformationPopup({ open: true, title: title, subTitle: subTitle });
  };

  const handleClosePopups = () => {
    setConformationPopup({ open: false, title: "", subTitle: "" });
    navigation(-1);
  };

  if (isLoading) {
    return <AppLoader />;
  }

  return (
    <div className="flex flex-col w-full h-dvh pr-3">
      <div className="justify-end flex items-end">
        <div className="w-52 lg:w-64">
          <BussinessInfo />
        </div>
      </div>
      <form
        onSubmit={handleUpdateEvent}
        className="flex w-full h-[calc(100dvh-40px)] lg:h-[calc(100dvh-48px)] py-3 overflow-y-auto gap-3">
        {/* side bar */}
        <div className="w-80 lg:w-96 min-w-80 lg:min-w-96 flex flex-col p-6 gap-3 overflow-y-auto no-scrollbar bg-white rounded-2xl">
          <div className="px-2 flex items-center justify-between">
            <p className="text-black font-bold text-xl">Edit Event</p>
            <button type="button">
              <img src={AchiveIcon} className="size-5" />
            </button>
          </div>
          <div className="flex flex-col w-full">
            {previewImage || eventImage.length > 0 ? (
              <div className="w-full h-64 rounded-12px overflow-hidden relative">
                <Image
                  src={previewImage || eventImage[0].preview}
                  fallbackSrc={PlaceholderImg}
                />
                <Button
                  type="button"
                  variant={"ghost"}
                  size={"icon"}
                  className="absolute top-2 right-2 h-6 w-6"
                  onClick={() => {
                    setEventImage([]);
                    setPreviewImage(null);
                  }}>
                  <X className="text-fb-warn-600" />
                </Button>
              </div>
            ) : (
              <div
                {...getRootProps({ className: "dropzone" })}
                className="bg-fb-neutral-100 rounded-12px h-64 flex justify-center items-center cursor-pointer border-2 border-dashed border-fb-neutral-400 relative overflow-hidden">
                <input {...getInputProps()} />
                <Image
                  src={PlaceholderImg}
                  className="absolute inset-0 w-full h-full object-cover opacity-50"
                />
                <div className="text-center relative z-10">
                  <p className="text-lg font-semibold text-fb-neutral-700">
                    Add an image here
                  </p>
                  <p className="text-sm text-fb-neutral-500">
                    Click or drag 'n' drop
                  </p>
                </div>
              </div>
            )}
          </div>
          <div className="flex flex-col gap-1">
            <Label htmlFor="EventName" className="text-fb-neutral-400 pl-3">
              Event Name
            </Label>
            <Input
              required
              id="EventName"
              value={eventName}
              onChange={(e) => setEventName(e.target.value)}
              className="!rounded-12px border-fb-neutral-400"
              placeholder="Name"
            />
          </div>
          <div className="flex flex-col gap-1">
            <Label className="text-fb-neutral-400 pl-3">Choose Calendar(s)</Label>
            <div className="grid grid-cols-3 gap-x-2 gap-y-1 mb-1">
              {apiCalendars?.slice(0, 3).map((cal, ind) => (
                <div
                  onClick={() => {
                    setChooseCalendar(prev => {
                      if (prev.includes(cal._id)) {
                        return prev.filter(id => id !== cal._id);
                      } else {
                        return [...prev, cal._id];
                      }
                    });
                  }}
                  key={ind}
                  className={cn(
                    "border rounded-8px cursor-pointer h-6 px-1 gap-1 flex items-center",
                    chooseCalendar.includes(cal._id)
                      ? "bg-fb-bPrime-100 border-fb-neutral-900 text-fb-neutral-800"
                      : "bg-fb-neutral-50 border-fb-neutral-500 text-fb-neutral-600"
                  )}>
                  <div className={cn(
                    "w-3 h-3 min-w-3 rounded-full",
                    chooseCalendar.includes(cal._id) 
                      ? "bg-fb-bPrime-500" 
                      : "bg-fb-neutral-400"
                  )} />
                  <p className="truncate text-xs font-semibold">
                    {cal.calendarName}
                  </p>
                </div>
              ))}
            </div>
          </div>
       
          <Accordion type="single" collapsible>
            <AccordionItem value="item-1">
              <AccordionContent>
                {apiCalendars && apiCalendars.length > 3 && (
                  <div className="grid grid-cols-3 gap-x-2 gap-y-0">
                    {apiCalendars.slice(3).map((cal, ind) => (
                      <div
                        onClick={() => {
                          setChooseCalendar(prev => {
                            if (prev.includes(cal._id)) {
                              return prev.filter(id => id !== cal._id);
                            } else {
                              return [...prev, cal._id];
                            }
                          });
                        }}
                        key={ind}
                        className={cn(
                          "border rounded-8px cursor-pointer h-6 px-1 gap-1 flex items-center",
                          chooseCalendar.includes(cal._id)
                            ? "bg-fb-bPrime-100 border-fb-neutral-900 text-fb-neutral-800"
                            : "bg-fb-neutral-50 border-fb-neutral-500 text-fb-neutral-600"
                        )}>
                        <div className={cn(
                          "w-3 h-3 min-w-3 rounded-full",
                          chooseCalendar.includes(cal._id) 
                            ? "bg-fb-bPrime-500" 
                            : "bg-fb-neutral-400"
                        )} />
                        <p className="truncate text-xs font-semibold">
                          {cal.calendarName}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </AccordionContent>
              <AccordionTrigger className="justify-end gap-2 text-sm text-fb-neutral-400 p-0">
                More
              </AccordionTrigger>
            </AccordionItem>
          </Accordion>
          
          <div className="flex items-center space-x-2">
            <Label htmlFor="all-day">All day</Label>
            <Switch
              checked={isAllDayEvent}
              onCheckedChange={setIsAllDayEvent}
              id="all-day"
              size="md"
            />
          </div>
          <div className="w-full flex flex-col gap-0.5 items-center justify-center mb-2">
            <div className="flex gap-1 items-center">
              <DateTimePicker
                date={startDate}
                setDate={(date) => {
                  setStartDate(date);
                  setEndDate(addHours(date, 1));
                }}
                isSmallView
                showDay={false}
              />
              <div className="w-12 h-0.5 rounded-full bg-black/70" />
              <DateTimePicker
                date={endDate}
                setDate={setEndDate}
                isSmallView
                showDay={false}
              />
            </div>
          </div>
          <div className="flex items-center justify-start gap-4">
            <div className="flex items-center gap-2">
                <Switch
                    id="reminder-switch"
                    checked={isReminderActive}
                    onCheckedChange={async (checked) => {
                        setIsReminderActive(checked);
                        if (checked) {
                            // Request permission when reminder is activated
                            const permission = await requestNotificationPermission();
                            if (permission === 'granted') {
                                setReminderTime("15-min");
                            } else {
                                // If permission not granted, revert switch and show message
                                setIsReminderActive(false);
                                toast.error("Notification permission denied. Please enable it in your browser settings to receive reminders.");
                            }
                        } else {
                            setReminderTime(null);
                        }
                    }}
                />
                <Label htmlFor="reminder-switch" className="text-fb-neutral-400 font-bold">Reminder</Label>
            </div>
            {isReminderActive && (
                <Select
                onValueChange={setReminderTime}
                value={reminderTime || ""}>
                <SelectTrigger className="!rounded-12px border-fb-neutral-400 w-fit h-8">
                    <SelectValue placeholder="Select reminder time" />
                </SelectTrigger>
                <SelectContent>
                    {ReminderEventTime.map((remind) => (
                    <SelectItem key={remind.value} value={remind.value}>
                        {remind.name}
                    </SelectItem>
                    ))}
                </SelectContent>
                </Select>
            )}
          </div>
        </div>

        {/* main content */}
        <div className="flex flex-col gap-3 w-full">
          <div className="flex flex-col gap-2 p-6 w-full h-full bg-white rounded-2xl overflow-y-auto no-scrollbar">
            <div className="flex justify-end gap-1">
              <Button
                size={"sm"}
                variant={"ghost"}
                type="button"
                className="rounded-full h-7 text-fb-warn-500"
                onClick={() => navigation("/")}>
                Cancel
                <X className="size-4 text-black" />
              </Button>
            </div>

            <div className="grid grid-cols-7 gap-4">
              <div className="col-span-2">
                <p className="text-fb-neutral-600 font-semibold text-lg">
                  Location :
                </p>
              </div>
              <div className="col-span-5 gap-2 flex flex-col">
                <div className="flex items-center relative">
                  <Input
                    value={location}
                    onChange={(e) => setLocation(e.target.value)}
                    className="!rounded-12px border-fb-neutral-400"
                    placeholder="Location"
                  />
                </div>
              </div>
            </div>
            <Separator />

            <div className="grid grid-cols-7 gap-4">
              <div className="col-span-2">
                <p className="text-fb-neutral-600 font-semibold text-lg">
                  Description :
                </p>
              </div>
              <div className="col-span-5 gap-1">
                <Textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className="!rounded-12px border-fb-neutral-400 resize-none"
                  placeholder="Description"
                />
              </div>
            </div>
            <Separator />

            <div className="grid grid-cols-7 gap-4">
              <div className="col-span-2">
                <p className="text-fb-neutral-600 font-semibold text-lg">
                  Repeat this event :
                </p>
              </div>
              <div className="col-span-5 grid grid-cols-4 gap-1">
                {RepeatEventTime.map((remind, ind) => (
                  <div
                    key={ind}
                    onClick={() => {
                      setRepeatTime(remind.value);
                      // Set repeat end date to 3 months from current date when repeat is selected
                      if (remind.value !== "Never") {
                        setRepeatEndDate(addMonths(new Date(), 3));
                      } else {
                        setRepeatEndDate(null);
                      }
                    }}
                    className={cn(
                      "h-9 rounded-12px border cursor-pointer flex items-center justify-center text-sm",
                      repeatTime === remind.value
                        ? "text-fb-neutral-700 border-fb-neutral-600 bg-fb-neutral-100"
                        : "text-fb-neutral-600 border-fb-neutral-400 bg-white"
                    )}
                  >
                    {remind.name}
                  </div>
                ))}
              </div>
            </div>
            <Separator />

            {repeatTime !== "Never" && repeatTime !== null && (
              <>
                <div className="grid grid-cols-7 items-center">
                  <div className="col-span-2 ">
                    <p className="text-fb-neutral-600 font-semibold text-lg">
                      Repeat Every :
                    </p>
                  </div>
                  <div className="col-span-5">
                    <Select
                      onValueChange={setRepeatEveryValue}
                      value={repeatEveryValue}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select a number" />
                      </SelectTrigger>
                      <SelectContent>
                        {RepeatEveryTime.map((item) => (
                          <SelectItem key={item.value} value={item.value}>
                            {item.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-7 items-center">
                  <div className="col-span-2 ">
                    <p className="text-fb-neutral-600 font-semibold text-lg">
                      Repeat End Date :
                    </p>
                  </div>
                  <div className="col-span-5">
                    <DateTimePicker
                      date={repeatEndDate || undefined}
                      setDate={setRepeatEndDate}
                      isSmallView
                      showDay={true}
                      disableTimeSelection={true}
                    />
                  </div>
                </div>
              </>
            )}

            <div className="grid grid-cols-7 gap-4">
              <div className="col-span-2">
                <p className="text-fb-neutral-600 font-semibold text-lg">
                  Add Links :
                </p>
              </div>
              <div className="col-span-5 gap-2 flex flex-col">
                {attachedLinks.map((links, ind) => (
                  <div className="relative flex items-center" key={ind}>
                    <Input
                      value={links}
                      type="text"
                      className="!rounded-12px border-fb-neutral-400 pr-8"
                      placeholder="Enter Link"
                      onChange={(e) => handleLinkChange(ind, e.target.value)}
                    />
                    <Button
                      type="button"
                      variant={"ghost"}
                      size={"icon"}
                      className="absolute right-1 !h-7 !w-7 rounded-full"
                      onClick={() => handleLinkRemove(ind)}>
                      <X />
                    </Button>
                  </div>
                ))}
                <Button
                  type="button"
                  variant={"outline"}
                  className="!rounded-12px border-fb-neutral-400 text-fb-neutral-600"
                  onClick={handleAddLink}>
                  Add a New Link
                </Button>
              </div>
            </div>
            <Separator />

            {/* <div className="grid grid-cols-7 gap-4">
              <div className="col-span-2">
                <p className="text-fb-neutral-600 font-semibold text-lg">
                  User Interactions :
                </p>
              </div>
              <div className="col-span-5 gap-2 flex flex-col">
                <div className="flex items-center gap-3 justify-between">
                  <Label
                    htmlFor="option-RSVP"
                    className="text-sm lg:text-base -tracking-wider font-normal flex gap-0.5 text-center cursor-pointer text-fb-neutral-600">
                    Provide RSVP
                    <img src={InfoIcon} alt="" className="w-4 h-4" />
                  </Label>
                  <Switch id="option-RSVP" size="md" />
                </div>
                <div className="flex items-center gap-3 justify-between">
                  <Label
                    htmlFor="allow-comments"
                    className="text-sm lg:text-base -tracking-wider font-normal flex gap-0.5 text-center cursor-pointer text-fb-neutral-600">
                    Allow Comments
                    <img src={InfoIcon} alt="" className="w-4 h-4" />
                  </Label>
                  <Switch id="allow-comments" size="md" />
                </div>
                <div className="flex items-center gap-3 justify-between">
                  <Label
                    htmlFor="RSVP-comments"
                    className="text-sm lg:text-base -tracking-wider font-normal flex gap-0.5 cursor-pointer text-fb-neutral-600">
                    Allow Users see likes
                    <img src={InfoIcon} alt="" className="w-4 h-4" />
                  </Label>
                  <Switch id="RSVP-comments" size="md" />
                </div>
              </div>
            </div> */}
            {/* <Separator /> */}

            <div className="flex justify-end gap-3">
              <Button
                type="button"
                disabled={isPending}
                className="bg-fb-neutral-300 w-80 hover:bg-neutral-200  text-red-500 !rounded-12px"
                onClick={handleDeleteEvent}>
                <Trash2 className="w-4 h-4 text-red-500" /> Delete
              </Button>
              <Button
                type="submit"
                disabled={isUpdating || isPending || isCreating}
                className="bg-fb-bPrime-600 !rounded-12px w-80">
                {isUpdating || isCreating ? "Saving..." : "Update Event"}
              </Button>
            </div>
          </div>
        </div>
      </form>

      <ConformationPopup
        isOpen={conformationPopup.open}
        title={conformationPopup.title}
        subTitle={conformationPopup.subTitle}
        onCLose={handleClosePopups}
      />

      {showUpdateAlert && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4 drop-shadow-buttonShadow">
            <h3 className="font-semibold text-xl -tracking-wide leading-6 mb-2">
              Update Recurring Event
            </h3>
            <Separator className="bg-fb-bPrime-hgts mb-4" />
            <p className="text-sm text-black/75 mb-6">
              This is a recurring event. Choose how you want to update it:
            </p>
            <div className="flex flex-col gap-2">
              <Button
                onClick={() => processUpdateEvent("this")}
                disabled={!eventDate}
                className="bg-fb-neutral-50 w-full hover:bg-neutral-100 h-8 text-black drop-shadow-buttonShadow"
              >
                This Event Only
              </Button>
              <Button
                onClick={() => processUpdateEvent("future")}
                disabled={!eventDate}
                className="bg-fb-neutral-50 w-full hover:bg-neutral-100 h-8 text-black drop-shadow-buttonShadow"
              >
                This and Future Events
              </Button>
              <Button
                onClick={() => processUpdateEvent("all")}
                className="bg-fb-neutral-50 w-full hover:bg-neutral-100 h-8 text-black drop-shadow-buttonShadow"
              >
                All Events in Series
              </Button>
              <Button
                onClick={() => setShowUpdateAlert(false)}
                className="bg-fb-neutral-50 w-full hover:bg-neutral-100 h-8 text-black drop-shadow-buttonShadow mt-2"
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}

      {showDeleteAlert && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4 drop-shadow-buttonShadow">
            {eventData?.repeat ? (
              <>
                <h3 className="font-semibold text-xl -tracking-wide leading-6 mb-2">
                  Delete Recurring Event
                </h3>
                <Separator className="bg-fb-bPrime-hgts mb-4" />
                <p className="text-sm text-black/75 mb-6">
                  This is a recurring event. Choose how you want to delete it:
                </p>
                <div className="flex flex-col gap-2">
                  <Button
                    onClick={thisEventOnly}
                    disabled={!eventDate}
                    className="bg-fb-neutral-50 w-full hover:bg-neutral-100 h-8 text-black drop-shadow-buttonShadow"
                  >
                    This Event Only
                  </Button>
                  <Button
                    onClick={thisAndFutureEvents}
                    disabled={!eventDate}
                    className="bg-fb-neutral-50 w-full hover:bg-neutral-100 h-8 text-black drop-shadow-buttonShadow"
                  >
                    This and Future Events
                  </Button>
                  <Button
                    onClick={handleDelete}
                    className="bg-fb-neutral-50 w-full hover:bg-neutral-100 h-8 text-red-500 drop-shadow-buttonShadow"
                  >
                    <Trash2 className="w-4 h-4 text-red-500 mr-2" />
                    All Events in Series
                  </Button>
                  <Button
                    onClick={() => setShowDeleteAlert(false)}
                    className="bg-fb-neutral-50 w-full hover:bg-neutral-100 h-8 text-black drop-shadow-buttonShadow mt-2"
                  >
                    Cancel
                  </Button>
                </div>
              </>
            ) : (
              <>
                <h3 className="font-semibold text-xl -tracking-wide leading-6 mb-2">
                  Delete Event
                </h3>
                <Separator className="bg-fb-bPrime-hgts mb-4" />
                <p className="text-sm text-black/75 mb-6">
                  Are you sure you want to delete this event? This action cannot
                  be undone.
                </p>
                <div className="flex justify-end gap-4">
                  <Button
                    onClick={() => setShowDeleteAlert(false)}
                    className="bg-fb-neutral-50 hover:bg-neutral-100 h-8 text-black drop-shadow-buttonShadow px-4"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleDelete}
                    className="bg-red-500 hover:bg-red-600 h-8 text-white drop-shadow-buttonShadow px-4 flex items-center"
                  >
                    <Trash2 className="w-4 h-4 text-white mr-2" />
                    Delete
                  </Button>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
