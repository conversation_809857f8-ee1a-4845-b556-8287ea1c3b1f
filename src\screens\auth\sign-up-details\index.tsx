import { toast } from "sonner";
import { X } from "lucide-react";
import { useState, FormEvent } from "react";
import { Navigate, useLocation, useNavigate } from "react-router";

import { AccountDataT } from "@/types";
import { generateUUID } from "@/utils";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/context/AuthContext";
import BgImage from "@/assets/images/bg-image.png";
import { Textarea } from "@/components/ui/textarea";
import GellaryIcon from "@/assets/svg/image-icon.svg";
import BussinessLogo from "@/assets/images/fragment-business-logo.png";
import { useCreateAccount, useUpdateAccountDetails } from "@/api/account";
import { useCustomDropzone, FILE_TYPES } from "@/hook/useCustomDropzone";

interface FileWithPreview extends File {
  preview: string;
}

export default function SignUpDetailsScreen() {
  const {
    state: { accoutData, userId },
  } = useLocation();

  const { handleLogin, userId: MainUserId } = useAuth();
  const navigate = useNavigate();

  const [yourId, setYourId] = useState<string>("");
  const [yourLogo, setYourLogo] = useState<FileWithPreview[]>([]);
  const [fragment, setFragment] = useState<string>("");
  const [yourBussinessDetail, setYourBussinessDetail] = useState<string>("");

  const { getRootProps, getInputProps } = useCustomDropzone({
    acceptedFileTypes: FILE_TYPES.IMAGES,
    maxFiles: 1,
    maxSize: 1 * 1024 * 1024, // 1MB
    multiple: false,
    showToastOnError: true,
    customErrorMessages: {
      "file-invalid-type":
        "Please upload only image files for your business logo",
      "file-too-large": "Business logo image is too large",
      "too-many-files": "You can only upload one business logo",
    },
    onFilesAccepted: (acceptedFiles) => {
      setYourLogo(
        acceptedFiles.map((file) =>
          Object.assign(file, {
            preview: URL.createObjectURL(file),
          })
        )
      );
    },
    // Note: No onFilesRejected callback - errors will be handled automatically by the hook
  });

  const { mutate, isPending } = useCreateAccount();
  const { mutate: updateAccount, isPending: isUpdating } =
    useUpdateAccountDetails();

  const handleAccount = (e: FormEvent) => {
    e.preventDefault();

    if (accoutData) {
      handleUpdateAccount();
    } else {
      handleCreateAccount();
    }
  };

  const handleCreateAccount = () => {
    const NewData: AccountDataT = {
      usrId: userId,
      _id: generateUUID("bizAcc"),
      blocked: false,
      deleted: false,
      businessId: yourId,
      businessName: fragment,
      website: "",
      location: "",
      email: "",
      picture: "",
      description: yourBussinessDetail,
      instagram: "",
      linkedIn: "",
      pictureImage: yourLogo[0] || undefined,
    };

    mutate(NewData, {
      onSuccess: () => {
        toast.success("Account created successfully");
        handleLogin(NewData.usrId);
        navigate("/");
      },
      onError: (error) => {
        toast.error(error.message);
      },
    });
  };

  const handleUpdateAccount = () => {
    const NewData: AccountDataT = {
      usrId: userId,
      _id: accoutData?._id,
      blocked: false,
      deleted: false,
      businessId: yourId,
      businessName: fragment,
      website: "",
      location: "",
      email: "",
      picture: "",
      description: yourBussinessDetail,
      instagram: "",
      linkedIn: "",
      pictureImage: yourLogo[0] || undefined,
    };

    updateAccount(NewData, {
      onSuccess: () => {
        toast.success("Account created successfully...");
        handleLogin(NewData.usrId);
        navigate("/");
      },
      onError: (error) => {
        toast.error(error.message);
      },
    });
  };

  const isLoading = isPending || isUpdating;

  if (!userId || MainUserId) {
    return <Navigate to="/" replace />;
  }

  return (
    <div className="bg-fb-bPrime-600 flex w-full h-dvh sm:h-screen relative overflow-hidden">
      <img
        src={BgImage}
        alt=""
        className="absolute z-0 w-full h-full opacity-20"
      />

      <div className="flex h-full px-4 py-6 w-1/2 z-10 ">
        <div className="bg-white flex w-full rounded-3xl px-6 sm:px-12 md:px-16 lg:px-24 justify-center items-center overflow-y-auto drop-shadow-cardOutShadow shadow-cardInnerShadow">
          <form
            className="flex flex-col gap-12 w-full justify-center"
            onSubmit={handleAccount}>
            <div className="border-b pb-1 border-black">
              <p className="text-3xl text-black">Build Your Business</p>
            </div>
            <div className="flex flex-col gap-3 w-full   h-full">
              <div className="flex flex-col gap-1 w-full">
                <Label
                  htmlFor="fragment"
                  className="text-black font-semibold text-2xl">
                  Business Name*
                </Label>
                <Input
                  required
                  id="fragment"
                  value={fragment || ""}
                  type="text"
                  placeholder="fragment"
                  onChange={(e) => setFragment(e.target.value)}
                  className=""
                />
              </div>
              <div className="flex flex-col gap-1 w-full">
                <Label
                  htmlFor="yourId"
                  className="text-black font-semibold text-2xl">
                  Choose your ID*
                </Label>
                <Input
                  required
                  id="yourId"
                  value={yourId || ""}
                  type="text"
                  placeholder="@fragBus"
                  onChange={(e) => setYourId(e.target.value)}
                  className=""
                />
              </div>
              <div className="flex w-full gap-3">
                <div className="w-full">
                  <p className="text-black font-semibold text-xl pl-3">
                    What dou you do?
                  </p>
                  <Textarea
                    value={yourBussinessDetail || ""}
                    onChange={(e) => setYourBussinessDetail(e.target.value)}
                    placeholder="Tell us about your business..."
                    className="resize-none h-24 "
                  />
                </div>
                <div className="w-full">
                  <p className="text-black font-semibold text-xl pl-3">
                    Add your Logo
                  </p>
                  {yourLogo.length > 0 ? (
                    <div className="w-full h-24 rounded-xl overflow-hidden relative">
                      <img
                        src={yourLogo[0]?.preview}
                        alt=""
                        className="w-full h-full object-cover"
                      />
                      <Button
                        type="button"
                        size={"icon"}
                        variant={"ghost"}
                        className="absolute top-2 right-2 !w-4 !h-4"
                        onClick={() => setYourLogo([])}>
                        <X className="!size-4 text-red-500" />
                      </Button>
                    </div>
                  ) : (
                    <div
                      {...getRootProps({ className: "dropzone" })}
                      className="bg-fb-neutral-200 rounded-xl h-24 drop-shadow-inputShadow border border-black flex justify-center items-center cursor-pointer">
                      <input {...getInputProps()} />
                      <img src={GellaryIcon} className="h-16" />
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="flex flex-col gap-4 w-full">
              <div className="flex flex-col gap-1 w-full">
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="text-xl font-semibold rounded-full text-white bg-fb-bPrime-600">
                  Sign Up
                </Button>
              </div>
            </div>
          </form>
        </div>
      </div>
      <div className="flex justify-center items-center w-1/2 z-10">
        <div className="flex flex-col">
          <div className="w-48 h-48">
            <img
              src={BussinessLogo}
              alt=""
              className="w-full h-full object-cover"
            />
          </div>
          <p className="text-3xl font-bold text-white">Connect with</p>
          <p className="text-[44px] leading-none font-bold text-white">
            Everyone
          </p>
        </div>
      </div>
    </div>
  );
}
