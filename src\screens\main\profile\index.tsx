import { toast } from "sonner";
import { X } from "lucide-react";
import { useEffect, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowUpRightFromSquare } from "@fortawesome/free-solid-svg-icons";

import { cn } from "@/lib/utils";
import { AccountDataT, FileWithPreview } from "@/types";
import { ProfileFieldValue } from "@/enums";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { ProfileFields } from "@/constant/data";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import EditIcon from "@/assets/svg/edit-icon.svg";
import { Textarea } from "@/components/ui/textarea";
import PhoneIcon from "@/assets/svg/phone-icon.svg";
import { Separator } from "@/components/ui/separator";
import DeleteIcon from "@/assets/svg/delete-icon.svg";
import LaptopIcon from "@/assets/svg/laptop-icon.svg";
import { AlertPopup } from "@/components/common/alert-popup";
import { CoonectedUsers } from "@/constant/dummy";
import { ConformationPopup } from "@/components/common/conformation-popup";
import { useGetAccounDetails, useUpdateAccountDetails } from "@/api/account";
import { DeleteProfileReviewPopup } from "@/components/common/delete-profile-review-popup";
import { Image } from "@/components/ui/image";
import { useAuth } from "@/context/AuthContext";
import GellaryIcon from "@/assets/svg/image-icon.svg";
import { useCustomDropzone, FILE_TYPES } from "@/hook/useCustomDropzone";

export default function UserProfileScreen() {
  const { mutate, isPending } = useUpdateAccountDetails();
  const { userId } = useAuth();
  const { data: AccountData } = useGetAccounDetails({
    userId: userId,
  });

  const [currentField, setCurrentField] = useState<ProfileFieldValue>(
    ProfileFieldValue.DETAILS
  );
  const [isUserEdit, setIsUserEdit] = useState(false);

  const [selectedFile, setSelectedFile] = useState<FileWithPreview[]>([]);
  const [profileImage, setProfileImage] = useState("");

  const [accountDetails, setAccountDetails] = useState<AccountDataT>({
    _id: "",
    businessName: "",
    businessId: "",
    picture: "",
    website: "",
    location: "",
    email: "",
    instagram: "",
    linkedIn: "",
    deleted: false,
    blocked: false,
    description: "",
    usrId: "",
  });

  const [workFileds, setWorkFields] = useState<string[]>([]);

  const [conformationPopup, setConformationPopup] = useState({
    open: false,
    title: "",
    subTitle: "",
  });

  const [alertPopup, setAlertPopup] = useState({
    open: false,
    title: "",
  });

  const setOldData = (data: AccountDataT) => {
    setAccountDetails(data);
    setProfileImage(data.picture || "");
  };

  useEffect(() => {
    if (AccountData && AccountData?.accData?.[0] && !isUserEdit) {
      setOldData(AccountData?.accData?.[0]);
    }
  }, [AccountData, isUserEdit]);

  const { getRootProps, getInputProps } = useCustomDropzone({
    acceptedFileTypes: FILE_TYPES.IMAGES,
    maxFiles: 1,
    maxSize: 1 * 1024 * 1024, // 1MB
    multiple: false,
    showToastOnError: true,
    customErrorMessages: {
      "file-invalid-type":
        "Please upload only image files (JPG, PNG, GIF, WebP)",
      "file-too-large": "Profile image is too large",
      "too-many-files": "You can only upload one profile image",
    },
    onFilesAccepted: (acceptedFiles) => {
      setSelectedFile(
        acceptedFiles.map((file) => {
          const preview = URL.createObjectURL(file);
          setProfileImage(preview);
          return Object.assign(file, {
            preview: preview,
          });
        })
      );
    },
    onFilesRejected: () => {
      setSelectedFile([]);
    },
  });

  const handleUpdateAccount = () => {
    console.log(accountDetails, "--update account data--");

    mutate(
      { ...accountDetails, pictureImage: selectedFile?.[0] || undefined },
      {
        onSuccess() {
          handleSetConformation({
            title: "The changes have been saved to your profile",
            subTitle: "",
          });
        },
        onError(error) {
          toast.error(error.message);
        },
      }
    );
  };

  const handleChangeDetailValue = (
    val: string | boolean,
    keyName: keyof AccountDataT
  ) => {
    setAccountDetails((old) => ({ ...old, [keyName]: val }));
  };

  const RemoveWorkField = (ind: number) => {
    setWorkFields((old) => old.filter((_, index) => index !== ind));
  };

  const Settings = () => {
    return (
      <div className="flex flex-col gap-5 px-10 py-20">
        <div className="flex flex-col gap-2">
          <p className="text-lg lg:text-xl font-semibold">Website Settings</p>
          <div className="w-96 justify-between flex items-center">
            <p className="text-sm lg:text-lg font-semibold">Notifications</p>
            <Switch />
          </div>
        </div>
        <Separator className="bg-black" />
        <div className="flex flex-col gap-4">
          <p className="text-base lg:text-xl font-semibold">
            Devices connected to this account
          </p>
          <div className="flex flex-col gap-2">
            {CoonectedUsers.map((conect, ind) => {
              return (
                <div className="flex items-center gap-2 " key={ind}>
                  {conect.isPc && <img src={LaptopIcon} alt="" />}
                  {conect.isMobile && <img src={PhoneIcon} alt="" />}
                  <p className="text-sm lg:text-lg">
                    System name/{conect.name}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  const SyncAllDevice = () => {
    return (
      <div className="flex justify-center items-center px-10 py-20 w-full h-full">
        <div className="flex flex-col gap-2 justify-center items-center">
          <div className="flex gap-8">
            <div className="flex flex-col justify-center items-center ">
              <img src={LaptopIcon} alt="" className="h-32" />
              <p className="flex items-center gap-2 text-fb-neutral-900">
                <Input
                  type="radio"
                  defaultChecked
                  className="!text-fb-bPrime-600 checked:!text-fb-bPrime-600"
                />
                Website
              </p>
            </div>
            <div className="flex flex-col justify-center items-center">
              <img src={PhoneIcon} alt="" className="h-32" />
              <p className="flex items-center gap-2 text-fb-neutral-900">
                <Input
                  type="radio"
                  defaultChecked
                  className="!text-fb-bPrime-600 checked:!text-fb-bPrime-600"
                />
                Application
              </p>
            </div>
          </div>
          <p className="text-sm lg:text-lg text-fb-neutral-900 italic">
            All systems synced
          </p>
        </div>
      </div>
    );
  };

  const RenderFileds = () => {
    switch (currentField) {
      case ProfileFieldValue.SETTINGS:
        return <Settings />;
      case ProfileFieldValue.SyncAllDevices:
        return <SyncAllDevice />;

      default:
        break;
    }
  };

  const handleClosePopups = () => {
    setConformationPopup({ open: false, title: "", subTitle: "" });
    setAlertPopup({ open: false, title: "" });
    setIsUserEdit(false);
  };

  const handleSetConformation = ({
    title,
    subTitle,
  }: {
    title: string;
    subTitle: string;
  }) => {
    setConformationPopup({ open: true, title: title, subTitle: subTitle });
  };

  const handleSetAlert = ({ title }: { title: string }) => {
    setAlertPopup({ open: true, title: title });
  };

  return (
    <div className="flex flex-col gap-1 py-3 pr-3 w-full h-dvh">
      <div className="flex w-full px-6 mt-2">
        <p className="text-base lg:text-xl">{accountDetails.businessName}</p>
      </div>

      <div className="bg-white w-full h-full py-3 flex rounded-2xl overflow-hidden">
        <div className="lg:w-80 min-w-64 w-64 lg:min-w-80 h-full py-3 p-6 pr-3 overflow-y-auto no-scrollbar">
          <div
            {...getRootProps()}
            className="w-full h-72 bg-fb-neutral-200 rounded-xl flex flex-col overflow-hidden justify-center items-center gap-2 relative">
            {isUserEdit ? (
              <img
                src={profileImage || GellaryIcon}
                alt="Profile"
                className="w-full h-full object-cover cursor-pointer"
              />
            ) : (
              <Image src={profileImage} alt="Profile" />
            )}

            {!profileImage && isUserEdit && (
              <input {...getInputProps()} className="z-20" />
            )}
            {isUserEdit && (
              <Button
                size={"icon"}
                onClick={(e) => {
                  e.stopPropagation();
                  setProfileImage("");
                  setSelectedFile([]);
                }}
                className="absolute bottom-3 right-3 bg-fb-cadelBlue-600 hover:bg-fb-cadelBlue-600/90 !rounded-12px">
                <img src={DeleteIcon} alt="" className="w-7 h-7" />
              </Button>
            )}
          </div>
          <div className="flex flex-col gap-3 py-5 w-full">
            {ProfileFields.map((row, ind) => (
              <Button
                key={ind}
                onClick={() => {
                  setCurrentField(row.value);
                  setIsUserEdit(false);
                }}
                disabled={isUserEdit}
                className={cn(
                  "rounded-full text-xs lg:text-sm duration-300 transition-all h-7 lg:h-9",
                  currentField === row.value
                    ? "bg-fb-bPrime-500 text-white"
                    : "bg-fb-neutral-50 text-black hover:bg-gray-300 drop-shadow-buttonShadow"
                )}>
                {row.name}
                {row.toTrasfer && (
                  <FontAwesomeIcon
                    icon={faArrowUpRightFromSquare}
                    className="!size-2.5 lg:!size-3.5"
                  />
                )}
              </Button>
            ))}
          </div>
        </div>
        {/* render fileds */}
        <div className="w-full overflow-y-auto no-scrollbar ">
          {currentField === ProfileFieldValue.DETAILS && (
            <div className="flex flex-col w-full gap-5 py-3 pl-3 pr-6  overflow-y-auto">
              <div className="flex justify-between w-full gap-1">
                {isUserEdit && (
                  <Button
                    disabled={isPending}
                    variant={"ghost"}
                    className="h-6 lg:h-7 text-xs lg:text-sm text-fb-warn-600"
                    onClick={() => {
                      handleSetAlert({
                        title: `Are you sure you want to go back? \n \n The changes are not saved to your Profile`,
                      });
                    }}>
                    Cancel
                  </Button>
                )}
                {isUserEdit ? (
                  <Button
                    disabled={isPending}
                    variant={"ghost"}
                    className="h-6 lg:h-7 text-xs lg:text-sm text-fb-success-600"
                    onClick={handleUpdateAccount}>
                    Save Changes
                  </Button>
                ) : (
                  <Button
                    className="px-4 ml-auto text-black text-xs lg:text-sm bg-fb-neutral-50 drop-shadow-buttonShadow h-6 lg:h-7 hover:bg-neutral-100"
                    onClick={() => setIsUserEdit(true)}>
                    <img src={EditIcon} alt="" className="size-3.5 lg:size-5" />{" "}
                    Edit
                  </Button>
                )}
              </div>
              <RenderDeatilsInputFiled
                isUserEdit={isUserEdit}
                labelName="Company ID"
                value={accountDetails.businessId}
                handleChange={(val: string) => {
                  handleChangeDetailValue(val, "businessId");
                }}
              />
              <RenderDeatilsInputFiled
                isUserEdit={isUserEdit}
                labelName="Email ID"
                value={accountDetails.email}
                handleChange={(val: string) => {
                  handleChangeDetailValue(val, "email");
                }}
              />
              <RenderDeatilsInputFiled
                isUserEdit={isUserEdit}
                labelName="Description"
                value={accountDetails.description}
                handleChange={(val: string) => {
                  handleChangeDetailValue(val, "description");
                }}
                isDescription
              />
              <div className="flex flex-wrap gap-1.5 py-1.5">
                {workFileds.map((row, ind) => {
                  return (
                    <div
                      className="px-3 h-6 lg:h-7 text-xs lg:text-sm items-center flex gap-1 bg-fb-neutral-50 drop-shadow-buttonShadow rounded-full font-medium"
                      key={ind}>
                      {row}{" "}
                      {isUserEdit && (
                        <Button
                          variant={"ghost"}
                          size={"icon"}
                          className="h-5 w-5"
                          onClick={() => RemoveWorkField(ind)}>
                          <X className="!size-4" />
                        </Button>
                      )}
                    </div>
                  );
                })}
                <Button className="bg-fb-bPrime-100 px-3 text-xs lg:text-sm h-6 lg:h-7 text-black hover:bg-fb-neutral-300 ">
                  Select more
                </Button>
              </div>
              <RenderDeatilsInputFiled
                isUserEdit={isUserEdit}
                labelName="Website link"
                value={accountDetails.website}
                handleChange={(val: string) => {
                  handleChangeDetailValue(val, "website");
                }}
              />
              <RenderDeatilsInputFiled
                isUserEdit={isUserEdit}
                labelName="Insta link"
                value={accountDetails.instagram}
                handleChange={(val: string) => {
                  handleChangeDetailValue(val, "instagram");
                }}
              />
              <RenderDeatilsInputFiled
                isUserEdit={isUserEdit}
                labelName="LinkedIn"
                value={accountDetails.linkedIn}
                handleChange={(val: string) => {
                  handleChangeDetailValue(val, "linkedIn");
                }}
              />
              {isUserEdit && <DeleteProfileReviewPopup />}
            </div>
          )}
          {RenderFileds()}
        </div>
      </div>
      <ConformationPopup
        isOpen={conformationPopup.open}
        title={conformationPopup.title}
        subTitle={conformationPopup.subTitle}
        onCLose={handleClosePopups}
      />
      <AlertPopup
        isOpen={alertPopup.open}
        title={alertPopup.title}
        onCLose={handleClosePopups}
        onSuccess={() => {
          if (AccountData) {
            setOldData(AccountData?.accData[0]);
          }
          handleClosePopups();
        }}
      />
    </div>
  );
}

const RenderDeatilsInputFiled = ({
  labelName,
  isUserEdit,
  value,
  handleChange,
  isDescription = false,
}: {
  labelName: string;
  isUserEdit: boolean;
  value: string;
  handleChange: (val: string) => void;
  isDescription?: boolean;
}) => {
  return (
    <div className="flex flex-col gap-0.5">
      <Label
        htmlFor={labelName.trim()}
        className="px-5 font-light text-xs lg:text-sm leading-5">
        {labelName}
      </Label>
      {!isDescription &&
        (isUserEdit ? (
          <Input
            id={labelName.trim()}
            value={value}
            className="h-8 lg:h-9 !text-sm"
            onChange={(e) => handleChange(e.target.value)}
          />
        ) : (
          <div className="min-h-9 px-4 py-1.5 text-xs lg:text-sm leading-6 font-medium shadow-boxViewShadow bg-fb-neutral-50 text-fb-neutral-900 rounded-full">
            {value}
          </div>
        ))}
      {isDescription &&
        (isUserEdit ? (
          <Textarea
            id={labelName.trim()}
            value={value}
            className=" h-20 resize-none !text-sm"
            onChange={(e) => handleChange(e.target.value)}
          />
        ) : (
          <div className="px-4 py-1.5 text-xs lg:text-sm leading-6 font-medium h-20 shadow-boxViewShadow bg-fb-neutral-50 text-fb-neutral-900 rounded-xl">
            {value}
          </div>
        ))}
    </div>
  );
};
