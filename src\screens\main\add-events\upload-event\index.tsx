import { useState } from "react";
import { Link, useNavigate } from "react-router";
import { X } from "lucide-react";

import { FileWithPreview } from "@/types";
import { Button } from "@/components/ui/button";
import AchiveIcon from "@/assets/svg/achive-icon.svg";
import BussinessInfo from "@/components/common/bussiness-info";

import { Separator } from "@/components/ui/separator";
import DownArrows from "@/assets/images/down-arrows.png";
import DownloadIcon from "@/assets/svg/download-icon.svg";
import { PdfIcon } from "@/assets/svg/icons";
import { useCustomDropzone, FILE_TYPES } from "@/hook/useCustomDropzone";

export default function AddEventWithUploadScreen() {
  const [files, setFiles] = useState<FileWithPreview[]>([]);
  const navigate = useNavigate();

  const { getRootProps, getInputProps } = useCustomDropzone({
    acceptedFileTypes: FILE_TYPES.EXCEL,
    maxFiles: 1,
    maxSize: 1 * 1024 * 1024, // 1MB
    multiple: false,
    showToastOnError: true,
    customErrorMessages: {
      "file-invalid-type": "Please upload valid Excel or CSV files",
      "file-too-large": "File size is too large. You can only upload up to 1MB",
      "too-many-files": "You can only upload 1 file",
    },
    onFilesAccepted: (acceptedFiles) => {
      setFiles(
        acceptedFiles.map((file) =>
          Object.assign(file, {
            preview: URL.createObjectURL(file),
          })
        )
      );
    },
    onFilesRejected: () => {
      setFiles([]);
    },
  });

  return (
    <div className="flex flex-col w-full h-dvh pr-3">
      <div className="justify-end flex items-end">
        <div className="w-52 lg:w-64">
          <BussinessInfo />
        </div>
      </div>
      <div className="flex w-full h-[calc(100dvh-40px)] lg:h-[calc(100dvh-48px)] py-3 overflow-y-auto gap-3">
        {/* side bar */}
        <div className="w-80 lg:w-96 min-w-80 lg:min-w-96 flex flex-col p-6 gap-3 overflow-y-auto no-scrollbar bg-white rounded-2xl">
          <div className="px-2  flex items-center justify-between">
            <p className="text-black font-bold text-xl">New Event</p>
            <button type="button" onClick={() => navigate(-1)}>
              <img src={AchiveIcon} className="size-5" />
            </button>
          </div>
          <div className="flex flex-col gap-4 px-2">
            <div className="flex flex-col gap-2">
              <p className="text-black font-bold text-xl">Upload Events</p>
              <p className="text-sm leading-4">
                If you have multiple events you can upload from template it all
                at once follow the steps below
              </p>
            </div>
            <div className="flex flex-col gap-4 ">
              <div className="fex flex-col gap-2">
                <div className="flex gap-2 items-center">
                  <div className="h-10 w-10 rounded-lg bg-fb-bPrime-50 flex justify-center items-center text-black text-2xl">
                    1
                  </div>
                  <p className="text-fb-neutral-700 text-sm">
                    Download the template below
                  </p>
                </div>
                <div className="">
                  <img
                    src={DownArrows}
                    className="w-full h-7 py-1 select-none"
                  />
                  <Button
                    variant={"default"}
                    className="bg-fb-bPrime-50 hover:bg-gray-300 text-black font-semibold drop-shadow-buttonShadow rounded-full h-8 w-full">
                    <img src={DownloadIcon} alt="" className="w-5 h-5" />{" "}
                    Download template
                  </Button>
                </div>
              </div>
              <div className="flex flex-col gap-3">
                <div className="flex gap-2 items-center">
                  <div className="h-10 w-10 rounded-lg bg-fb-bPrime-50 flex justify-center items-center text-black text-2xl">
                    2
                  </div>
                  <p className="text-fb-neutral-700">Fill in the data</p>
                </div>
                <div className="flex gap-2 items-center">
                  <div className="h-10 w-10 rounded-lg bg-fb-bPrime-50 flex justify-center items-center text-black text-2xl">
                    3
                  </div>
                  <p className="text-fb-neutral-700 text-sm">
                    Save and Upload the file as .XL, CSV
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/*  views */}
        <div className="flex flex-col flex-1 gap-2 ">
          <div className="border border-fb-neutral-40 rounded-12px p-2 flex gap-4 justify-around items-center">
            <p className="text-fb-neutral-700 text-lg ">OR</p>
            <p className="text-fb-neutral-700">Add event Manually</p>
            <Link to={"/add-event"}>
              <Button className="bg-fb-bPrime-500 h-8 w-96">
                Enter event Details
              </Button>
            </Link>
          </div>
          <div className="flex flex-col gap-2 p-6 w-full h-full bg-white rounded-2xl overflow-y-auto no-scrollbar">
            <div className="flex justify-end gap-1 ">
              <Button
                size={"sm"}
                variant={"ghost"}
                type="button"
                className="rounded-full h-7 text-fb-warn-500"
                onClick={() => navigate(-1)}>
                Cancel
                <X className="size-4 text-black" />
              </Button>
            </div>
            <div className="grid grid-cols-7">
              <div className="col-span-2 ">
                <p className="text-fb-neutral-600 font-semibold text-lg">
                  Upload your Files : <br /> (.xl or CSV)
                </p>
              </div>
              <div className="col-span-5 flex flex-col gap-3 border border-fb-neutral-400 rounded-12px bg-fb-neutral-50 p-4">
                <div
                  {...getRootProps({ className: "dropzone" })}
                  className="flex flex-col min-h-56 justify-center items-center px-6 py-4 gap-2 bg-white border border-fb-neutral-500 rounded-xl cursor-pointer">
                  <input {...getInputProps()} />

                  <PdfIcon className="text-fb-neutral-300 size-14" />
                  <p className="text-center text-lg leading-7 font-semibold text-fb-neutral-600">
                    Drag and Drop your files here
                  </p>
                  {/* <Button
                    size={"sm"}
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent event conflicts
                      open(); // Trigger file selection
                    }}
                    variant={"default"}
                    className="text-white rounded-full h-8 w-full bg-fb-bPrime-500 drop-shadow-buttonShadow">
                     Upload
                    Events File
                  </Button> */}
                </div>
                {files.map((row, ind) => {
                  return (
                    <Button
                      key={ind}
                      size={"sm"}
                      variant={"ghost"}
                      type="button"
                      className="!rounded-12px h-7 text-fb-neutral-600 justify-start bg-white px-4 gap-3">
                      <X className="size-4" />
                      {row.name}
                    </Button>
                  );
                })}
              </div>
            </div>
            <Separator />

            <div className="flex justify-end">
              <Button className="bg-fb-bPrime-600 !rounded-12px w-80">
                Add Event
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
