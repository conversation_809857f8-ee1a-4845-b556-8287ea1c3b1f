//public-event-details
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useGetCalendarDetails } from "@/api/calendar";
import { useGetSingleCalenderEvents } from "@/api/event";
import { useGetSimpleWebcalFeed } from "@/api/ics";
import { Image } from "@/components/ui/image";
import AppLoader from "@/components/common/app-loader";
import BgImage from "@/assets/images/bg-image.png";
import SadCalendar from "@/components/common/sad-calendar";
import { Button } from "@/components/ui/button";
import Fragmentlogo from "@/assets/images/fragment-EU-logo.png";
import { BASE_URL, WEBSITE_URL } from "@/envs";
import { useGetAccounDetails } from "@/api/account";
import EventDetailsView from "@/screens/main/event-details/components/event-details";
import { ArrowLeft } from "lucide-react";
import { useState } from "react";
import GoogleCalendarIcon from "@/assets/images/google_calendar.png";
import AppleCalendarIcon from "@/assets/images/apple_calendar.png";

export default function PublicEventDetailsScreen() {
  const { id, eventId, eventDate } = useParams<{ id: string; eventId: string; eventDate: string }>();
  // console.log("cal:",id,"event:",eventId);
  const navigate = useNavigate();
  const [copyMessage, setCopyMessage] = useState("");

  useGetSimpleWebcalFeed(id || "");

  const {
    data: calendarData,
    isLoading: isCalendarLoading,
    error: calendarError,
  } = useGetCalendarDetails({
    calendarIds: id ? [id] : [],
  });

  const calendar = calendarData?.[0];

  const {
    data: accountResponse,
    isLoading: isAccountLoading,
  } = useGetAccounDetails({
    userId: calendar?.usrId,
  });

  const accountData = accountResponse?.accData?.[0];

  const {
    data: eventsData,
    isLoading: areEventsLoading,
    error: eventsError,
  } = useGetSingleCalenderEvents({
    calendarIds: id ? [id] : [],
  });

  const event = eventsData?.find((e) => e._id === eventId);

  const handleAddToGoogleCalendar = () => {
    if (!id) return;
    
    const webcalUrl = `webcal://${BASE_URL.replace(/^https?:\/\//, "")}/api/ics/webcal/${id}`;
    const googleCalendarUrl = `https://calendar.google.com/calendar/render?cid=${encodeURIComponent(webcalUrl)}`;
    
    window.open(googleCalendarUrl, "_blank");
  };

  const handleAddToAppleCalendar = () => {
    if (!id) return;
    const webcalUrl = `${BASE_URL.replace(
      /^https?:\/\//,
      ""
    )}/api/ics/webcal/${id}`;
    window.location.href = `webcal://${webcalUrl}`;
  };

  const handleAddEventToGoogleCalendar = () => {
    if (!eventId) return;
    
    const webcalUrl = `webcal://${BASE_URL.replace(/^https?:\/\//, "")}/api/ics/webcal-events/${eventId}`;
    const googleCalendarUrl = `https://calendar.google.com/calendar/render?cid=${encodeURIComponent(webcalUrl)}`;
    
    window.open(googleCalendarUrl, "_blank");
  };

  const handleAddEventToAppleCalendar = () => {
    if (!eventId) return;
    const webcalUrl = `${BASE_URL.replace(
      /^https?:\/\//,
      ""
    )}/api/ics/webcal-events/${eventId}`;
    window.location.href = `webcal://${webcalUrl}`;
  };

  const handleDownloadIcs = async () => {
    if (!id) return;
    
    try {
      const response = await fetch(`${BASE_URL}/api/ics/${id}`);
      
      if (!response.ok) {
        throw new Error('Failed to download ICS file');
      }
      
      const icsContent = await response.text();
      const blob = new Blob([icsContent], { type: 'text/calendar' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${calendar?.calendarName || 'calendar'}.ics`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading ICS file:', error);
      setCopyMessage("Failed to download ICS file");
      setTimeout(() => setCopyMessage(""), 3000);
    }
  };

  const handleCopyIcsUrl = async () => {
    if (!id) return;
    
    const icsUrl = `${BASE_URL}/api/ics/${id}`;
    
    try {
      await navigator.clipboard.writeText(icsUrl);
      setCopyMessage("ICS URL copied to clipboard!");
      setTimeout(() => setCopyMessage(""), 3000);
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      const textArea = document.createElement('textarea');
      textArea.value = icsUrl;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        setCopyMessage("ICS URL copied to clipboard!");
        setTimeout(() => setCopyMessage(""), 3000);
      } catch (fallbackError) {
        console.error('Fallback copy failed:', fallbackError);
        setCopyMessage("Failed to copy URL");
        setTimeout(() => setCopyMessage(""), 3000);
      }
      document.body.removeChild(textArea);
    }
  };

  // Add these new functions to handle single event ICS operations
const handleDownloadEventIcs = async () => {
  if (!eventId) return;
  
  try {
    const response = await fetch(`${BASE_URL}/api/ics/webcal-events/${eventId}`);
    
    if (!response.ok) {
      throw new Error('Failed to download event ICS file');
    }
    
    const icsContent = await response.text();
    const blob = new Blob([icsContent], { type: 'text/calendar' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${event?.title || 'event'}.ics`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error downloading event ICS file:', error);
    setCopyMessage("Failed to download event ICS file");
    setTimeout(() => setCopyMessage(""), 3000);
  }
};

const handleCopyEventIcsUrl = async () => {
  if (!eventId) return;
  
  const eventIcsUrl = `${BASE_URL}/api/ics/webcal-events/${eventId}`;
  
  try {
    await navigator.clipboard.writeText(eventIcsUrl);
    setCopyMessage("Event ICS URL copied to clipboard!");
    setTimeout(() => setCopyMessage(""), 3000);
  } catch (error) {
    console.error('Error copying to clipboard:', error);
    const textArea = document.createElement('textarea');
    textArea.value = eventIcsUrl;
    document.body.appendChild(textArea);
    textArea.select();
    try {
      document.execCommand('copy');
      setCopyMessage("Event ICS URL copied to clipboard!");
      setTimeout(() => setCopyMessage(""), 3000);
    } catch (fallbackError) {
      console.error('Fallback copy failed:', fallbackError);
      setCopyMessage("Failed to copy event URL");
      setTimeout(() => setCopyMessage(""), 3000);
    }
    document.body.removeChild(textArea);
  }
};
  if (isCalendarLoading || areEventsLoading || isAccountLoading) {
    return <AppLoader />;
  }

  if (calendarError || eventsError || !calendar) {
    return <SadCalendar text="Something went wrong" />;
  }

  return (
    <div 
      className="min-h-screen bg-cover bg-center bg-no-repeat relative"
      style={{ backgroundImage: `url(${BgImage})` }}
    >
      <div className="absolute inset-0 bg-black/30"></div>
      
      <div className="relative z-10">
        {/* Header */}
        <div className="bg-white/95 backdrop-blur-sm border-b px-4 md:px-6 py-4 flex justify-between items-center">
          <div className="flex items-center gap-2 md:gap-3">
            <div className="flex items-center gap-1">
              <Image 
                src={Fragmentlogo}
                alt="Fragment Logo" 
                className="w-8 h-8 md:w-10 md:h-10" 
              />
            </div>
            <span className="text-xs md:text-sm text-gray-500 ml-1 md:ml-2">Powered by</span>
            <span className="font-semibold text-gray-900 text-sm md:text-base">Fragment</span>
          </div>
          <Button 
            onClick={() => window.open(WEBSITE_URL, "_blank")}
            variant="ghost"
            className="text-xs md:text-sm font-medium text-gray-900 hover:text-gray-700 px-2 md:px-4"
          >
            Visit Website
          </Button>
        </div>

        {/* Account/Calendar Header */}
        {accountData ? (
          <div className="flex items-center justify-center py-6 md:py-12 px-4 md:px-6">
            <div className="flex flex-col md:flex-row items-center gap-4 md:gap-8 max-w-4xl mx-auto text-center md:text-left">
              <div className="w-20 h-20 md:w-32 md:h-32 rounded-2xl overflow-hidden border-4 border-white shadow-xl flex-shrink-0">
                <Image
                  src={accountData.picture ?? undefined}
                  alt="Account Avatar"
                  className="w-full h-full object-cover"
                />
              </div>
              
              <div className="flex flex-col">
                <h1 className="text-2xl md:text-5xl font-bold text-white mb-2 drop-shadow-lg">
                  {accountData.businessName}
                </h1>
                <p className="text-lg md:text-xl text-white/80 mb-4 md:mb-6 drop-shadow-md">
                  @{accountData.businessId}
                </p>
                <Button 
                  onClick={() => navigate(`/calendar/public/all/${calendar.usrId}`)}
                  variant="outline" 
                  className="border-white text-white hover:bg-white hover:text-gray-900 px-6 md:px-8 py-2 md:py-3 rounded-full font-medium bg-white/10 backdrop-blur-sm transition-all duration-300 shadow-lg w-fit mx-auto md:mx-0 text-sm md:text-base"
                >
                  View All the Calendars
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center py-6 md:py-12 px-4 md:px-6">
            <div className="flex flex-col md:flex-row items-center gap-4 md:gap-8 max-w-4xl mx-auto text-center md:text-left">
              <div className="w-20 h-20 md:w-32 md:h-32 rounded-2xl overflow-hidden border-4 border-white shadow-xl flex-shrink-0">
                <Image
                  src={calendar.picture ?? undefined}
                  alt="Calendar Avatar"
                  className="w-full h-full object-cover"
                />
              </div>
              
              <div className="flex flex-col">
                <h1 className="text-2xl md:text-5xl font-bold text-white mb-2 drop-shadow-lg">
                  {calendar.calendarName}
                </h1>
                <p className="text-lg md:text-xl text-white/80 mb-4 md:mb-6 drop-shadow-md">
                  @{calendar.calendarId}
                </p>
                <Button 
                  onClick={() => navigate(`/public-calendar/all/${calendar.usrId}`)}
                  variant="outline" 
                  className="border-white text-white hover:bg-white hover:text-gray-900 px-6 md:px-8 py-2 md:py-3 rounded-full font-medium bg-white/10 backdrop-blur-sm transition-all duration-300 shadow-lg w-fit mx-auto md:mx-0 text-sm md:text-base"
                >
                  View All the Calendars
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="max-w-[95%] mx-auto px-2 md:px-4 pb-8">
          <div className="bg-white/95 backdrop-blur-sm rounded-2xl md:rounded-3xl shadow-2xl overflow-hidden">
            
            {/* Desktop Layout (md and up) */}
            <div className="hidden md:flex min-h-[80vh]">
              {/* Left Panel - Desktop */}
              <div className="w-[450px] p-6 bg-gray-50/50 rounded-l-3xl">
                <div className="bg-slate-800 text-white rounded-l-3xl p-8 shadow-xl">
                  <div className="flex flex-col items-center mb-6">
                    <div className="w-20 h-20 mb-4 rounded-full overflow-hidden border-2 border-white/20 shadow-lg">
                      <Image
                        src={calendar.picture ?? undefined}
                        alt="Calendar Avatar"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <h3 className="text-lg font-bold text-center mb-2">
                      {calendar.calendarName}
                    </h3>
                    <p className="text-sm text-gray-300 font-medium mb-3">
                      @{calendar.calendarId}
                    </p>
                    <p className="text-xs text-gray-300 text-center leading-relaxed">
                      {calendar.description}
                    </p>
                  </div>

                  <div className="border-t border-slate-600/50 mb-6"></div>

                  <div className="bg-slate-700/50 rounded-2xl p-6 backdrop-blur-sm">
                    <p className="text-sm mb-6 font-medium text-center italic text-white">Add this calendar to your</p>
                    
                    <div className="bg-gradient-to-r from-cyan-400 to-cyan-500 text-slate-900 text-lg font-bold py-4 px-6 rounded-full text-center mb-6 shadow-lg flex items-center justify-center gap-3">
                      <Image 
                        src={Fragmentlogo}
                        alt="Fragment Logo" 
                        className="w-8 h-8" 
                      />
                      <span>Fragment</span>
                    </div>
                    
                    <p className="text-sm text-gray-300 leading-relaxed mb-8 text-center">
                      to get a chance to connect with organisers, RSVP, like events, make payments, and even create your own planning space.
                    </p>
                    
                    <p className="text-sm text-gray-300 mb-4 text-center italic">
                      You can also add calendar to your
                    </p>
                    
                    <div className="grid grid-cols-2 gap-3 mb-6">
                      <Button 
                        onClick={handleAddToGoogleCalendar} 
                        className="bg-white hover:bg-gray-50 text-slate-900 text-sm font-semibold py-3 px-3 rounded-full shadow-lg border-0 flex items-center justify-center gap-2"
                      >
                        <Image 
                          src={GoogleCalendarIcon}
                          alt="Google Calendar" 
                          className="w-5 h-5" 
                        />
                        <span className="text-xs">Google Calendar</span>
                      </Button>
                      <Button 
                        onClick={handleAddToAppleCalendar} 
                        className="bg-white hover:bg-gray-50 text-slate-900 text-sm font-semibold py-3 px-3 rounded-full shadow-lg border-0 flex items-center justify-center gap-2"
                      >
                        <Image 
                          src={AppleCalendarIcon}
                          alt="Apple Calendar" 
                          className="w-5 h-5" 
                        />
                        <span className="text-xs">Apple Calendar</span>
                      </Button>
                    </div>
                    
                    <div className="pt-4 border-t border-slate-600/50">
                      <p className="text-sm text-gray-300 mb-4 text-center">
                        Or download/share the calendar file
                      </p>
                      <div className="grid grid-cols-2 gap-3">
                        <Button 
                          onClick={handleDownloadIcs} 
                          className="bg-slate-600/50 hover:bg-slate-600/70 text-white text-xs font-medium py-3 px-2 rounded-full border border-slate-500/30 flex items-center justify-center gap-1"
                        >
                          📥 <span className="text-xs">Download ICS File</span>
                        </Button>
                        <Button 
                          onClick={handleCopyIcsUrl} 
                          className="bg-slate-600/50 hover:bg-slate-600/70 text-white text-xs font-medium py-3 px-2 rounded-full border border-slate-500/30 flex items-center justify-center gap-1"
                        >
                          🔗 <span className="text-xs">Copy ICS URL</span>
                        </Button>
                      </div>
                      {copyMessage && (
                        <p className="text-xs text-green-400 mt-3 text-center font-medium">
                          {copyMessage}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Panel - Desktop */}
              <div className="flex-1 bg-white/50 p-8">
                <Button
                  onClick={() => navigate(-1)}
                  variant="ghost"
                  className="flex items-center gap-2 mb-4 text-sm font-medium"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Back to Calendar
                </Button>

                {/* Desktop Event Actions - Updated */}
                {event && (
                  <div className="mb-6 bg-white/80 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-gray-200/50">
                    {/* Header row with title and main action buttons */}
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-sm font-medium text-gray-700">
                        Add this Event to your Schedule
                      </span>
                      <div className="flex items-center gap-3">
                        <Button
                          onClick={handleAddEventToGoogleCalendar}
                          className="bg-cyan-500 hover:bg-cyan-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 text-sm font-medium shadow-md transition-all duration-200"
                        >
                          <Image 
                            src={Fragmentlogo}
                            alt="Fragment Logo" 
                            className="w-4 h-4" 
                          />
                          Fragment
                        </Button>
                        <Button
                          onClick={handleAddEventToGoogleCalendar}
                          className="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-lg flex items-center gap-2 text-sm font-medium border border-gray-200 shadow-md transition-all duration-200"
                        >
                          <Image 
                            src={GoogleCalendarIcon}
                            alt="Google Calendar" 
                            className="w-4 h-4" 
                          />
                          Google Calendar
                        </Button>
                        <Button
                          onClick={handleAddEventToAppleCalendar}
                          className="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-lg flex items-center gap-2 text-sm font-medium border border-gray-200 shadow-md transition-all duration-200"
                        >
                          <Image 
                            src={AppleCalendarIcon}
                            alt="Apple Calendar" 
                            className="w-4 h-4" 
                          />
                          Apple Calendar
                        </Button>
                      </div>
                    </div>

                    {/* ICS file row */}
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">You can get ICS file</span>
                      <div className="flex items-center gap-3">
                        <Button 
                          onClick={handleDownloadEventIcs} 
                          className="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-lg flex items-center gap-2 text-sm font-medium border border-gray-200 shadow-md transition-all duration-200"
                        >
                          📥 <span>Download ICS</span>
                        </Button>
                        <Button 
                          onClick={handleCopyEventIcsUrl} 
                          className="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-lg flex items-center gap-2 text-sm font-medium border border-gray-200 shadow-md transition-all duration-200"
                        >
                          🔗 <span>Copy ICS URL</span>
                        </Button>
                      </div>
                    </div>
                    
                    {copyMessage && (
                      <p className="text-xs text-green-600 mt-3 text-center font-medium">
                        {copyMessage}
                      </p>
                    )}
                  </div>
                )}

                {event ? (
                  <EventDetailsView data={event} eventDate={eventDate} isPublicView={true} />
                ) : (
                  <div className="text-center py-20">
                    <SadCalendar text="Event not found" />
                  </div>
                )}
              </div>
            </div>

            {/* Mobile Layout (below md) */}
            <div className="block md:hidden">
              {/* Mobile Header with Back Button */}
              <div className="p-4 bg-white/90 backdrop-blur-sm">
                <Button
                  onClick={() => navigate(-1)}
                  variant="ghost"
                  className="flex items-center gap-2 mb-4 text-sm font-medium p-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Back to Calendar
                </Button>
              </div>

              {/* Mobile Event Content */}
              <div className="p-4">
                {/* Mobile Event Details */}
                <div className="bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-lg">
                  {event ? (
                    <EventDetailsView data={event} eventDate={eventDate} isPublicView={true} />
                  ) : (
                    <div className="text-center py-10">
                      <SadCalendar text="Event not found" />
                    </div>
                  )}
                </div>
                {/* Mobile Event Actions */}
                {event && (
                    <div className="mb-6 bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-gray-200/50">
                      <p className="text-sm font-medium text-gray-700 mb-3 text-center">
                        Add this Event to your Schedule
                      </p>
                      
                      {/* Fragment button first */}
                      <div className="mb-3">
                        <Button
                          onClick={handleAddEventToGoogleCalendar}
                          className="w-full bg-cyan-500 hover:bg-cyan-600 text-white px-4 py-3 rounded-lg flex items-center justify-center gap-2 text-sm font-medium shadow-md transition-all duration-200"
                        >
                          <Image 
                            src={Fragmentlogo}
                            alt="Fragment Logo" 
                            className="w-4 h-4" 
                          />
                          Fragment
                        </Button>
                      </div>

                      {/* Calendar buttons grid */}
                      <div className="grid grid-cols-2 gap-2 mb-4">
                        <Button
                          onClick={handleAddEventToGoogleCalendar}
                          className="bg-white hover:bg-gray-50 text-gray-700 px-3 py-3 rounded-lg flex items-center justify-center gap-1 text-xs font-medium border border-gray-200 shadow-md transition-all duration-200"
                        >
                          <Image 
                            src={GoogleCalendarIcon}
                            alt="Google Calendar" 
                            className="w-4 h-4" 
                          />
                          Google Calendar
                        </Button>
                        <Button
                          onClick={handleAddEventToAppleCalendar}
                          className="bg-white hover:bg-gray-50 text-gray-700 px-3 py-3 rounded-lg flex items-center justify-center gap-1 text-xs font-medium border border-gray-200 shadow-md transition-all duration-200"
                        >
                          <Image 
                            src={AppleCalendarIcon}
                            alt="Apple Calendar" 
                            className="w-4 h-4" 
                          />
                          Apple Calendar
                        </Button>
                      </div>

                      {/* ICS section with text and buttons */}
                      <div className="flex flex-col gap-2">
                        <p className="text-sm text-gray-600">You can get ICS file</p>
                        <div className="grid grid-cols-2 gap-2">
                          <Button 
                            onClick={handleDownloadEventIcs} 
                            className="bg-white hover:bg-gray-50 text-gray-700 text-xs font-medium py-3 px-2 rounded-lg border border-gray-200 flex items-center justify-center gap-1 shadow-md transition-all duration-200"
                          >
                            📥 <span>Download ICS</span>
                          </Button>
                          <Button 
                            onClick={handleCopyEventIcsUrl} 
                            className="bg-white hover:bg-gray-50 text-gray-700 text-xs font-medium py-3 px-2 rounded-lg border border-gray-200 flex items-center justify-center gap-1 shadow-md transition-all duration-200"
                          >
                            🔗 <span>Copy ICS URL</span>
                          </Button>
                        </div>
                      </div>
                      
                      {copyMessage && (
                        <p className="text-xs text-green-600 mt-2 text-center font-medium">
                          {copyMessage}
                        </p>
                      )}
                    </div>
                  )}

                {/* Mobile Calendar Info */}
                {/* <div className="mt-6 bg-slate-800 text-white rounded-xl p-6 shadow-xl">
                  <div className="flex flex-col items-center mb-6">
                    <div className="w-16 h-16 mb-4 rounded-full overflow-hidden border-2 border-white/20 shadow-lg">
                      <Image
                        src={calendar.picture ?? undefined}
                        alt="Calendar Avatar"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <h3 className="text-lg font-bold text-center mb-2">
                      {calendar.calendarName}
                    </h3>
                    <p className="text-sm text-gray-300 font-medium mb-3">
                      @{calendar.calendarId}
                    </p>
                    <p className="text-xs text-gray-300 text-center leading-relaxed">
                      {calendar.description}
                    </p>
                  </div>

                  <div className="border-t border-slate-600/50 mb-6"></div>

                  <div className="bg-slate-700/50 rounded-xl p-4 backdrop-blur-sm">
                    <p className="text-sm mb-4 font-medium text-center italic text-white">Add this calendar to your</p>
                    
                    <div className="bg-gradient-to-r from-cyan-400 to-cyan-500 text-slate-900 text-base font-bold py-3 px-4 rounded-full text-center mb-4 shadow-lg flex items-center justify-center gap-2">
                      <Image 
                        src={Fragmentlogo}
                        alt="Fragment Logo" 
                        className="w-6 h-6" 
                      />
                      <span>Fragment</span>
                    </div>
                    
                    <p className="text-xs text-gray-300 leading-relaxed mb-6 text-center">
                      to get a chance to connect with organisers, RSVP, like events, make payments, and even create your own planning space.
                    </p>
                    
                    <p className="text-sm text-gray-300 mb-3 text-center italic">
                      You can also add calendar to your
                    </p>
                    
                    <div className="grid grid-cols-2 gap-2 mb-4">
                      <Button 
                        onClick={handleAddToGoogleCalendar} 
                        className="bg-white hover:bg-gray-50 text-slate-900 text-xs font-semibold py-3 px-2 rounded-full shadow-lg border-0 flex items-center justify-center gap-1"
                      >
                        <Image 
                          src={GoogleCalendarIcon}
                          alt="Google Calendar" 
                          className="w-4 h-4" 
                        />
                        <span className="text-xs">Google</span>
                      </Button>
                      <Button 
                        onClick={handleAddToAppleCalendar} 
                        className="bg-white hover:bg-gray-50 text-slate-900 text-xs font-semibold py-3 px-2 rounded-full shadow-lg border-0 flex items-center justify-center gap-1"
                      >
                        <Image 
                          src={AppleCalendarIcon}
                          alt="Apple Calendar" 
                          className="w-4 h-4" 
                        />
                        <span className="text-xs">Apple</span>
                      </Button>
                    </div>
                    
                    <div className="pt-3 border-t border-slate-600/50">
                      <p className="text-xs text-gray-300 mb-3 text-center">
                        Or download/share the calendar file
                      </p>
                      <div className="grid grid-cols-2 gap-2">
                        <Button 
                          onClick={handleDownloadIcs} 
                          className="bg-slate-600/50 hover:bg-slate-600/70 text-white text-xs font-medium py-2 px-2 rounded-full border border-slate-500/30 flex items-center justify-center gap-1"
                        >
                          📥 <span className="text-xs">Download</span>
                        </Button>
                        <Button 
                          onClick={handleCopyIcsUrl} 
                          className="bg-slate-600/50 hover:bg-slate-600/70 text-white text-xs font-medium py-2 px-2 rounded-full border border-slate-500/30 flex items-center justify-center gap-1"
                        >
                          🔗 <span className="text-xs">Copy URL</span>
                        </Button>
                      </div>
                      {copyMessage && (
                        <p className="text-xs text-green-400 mt-2 text-center font-medium">
                          {copyMessage}
                        </p>
                      )}
                    </div>
                  </div>
                </div> */}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}