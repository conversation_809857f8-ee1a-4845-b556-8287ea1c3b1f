import { useEffect, useState } from "react";
import { Flame, CalendarIcon } from "lucide-react";
import { toast } from "sonner";
import { format } from "date-fns";

import { cn } from "@/lib/utils";
import { ToDoStatusValue } from "@/enums";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useUpdateToDo } from "@/api/to-do";
import { CreateToDoFormT } from "@/types/api";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { TodoListModelT } from "@/types";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

export default function UpdateToDoTask({
  accId,
  categorys,
  oldData,
  onComplete,
}: {
  accId: string | undefined;
  categorys: string[];
  oldData: TodoListModelT;
  onComplete: () => void;
}) {
  const { mutate, isPending } = useUpdateToDo();

  const [todoTask, setToDoTask] = useState("");
  const [whenToComplete, setWhenToComplete] = useState<string | null>(null);
  const [category, setCategory] = useState("");

  const [taskEmergancy, setTaskEmergancy] = useState<ToDoStatusValue>(
    ToDoStatusValue.NO_IMPORTANT
  );

  useEffect(() => {
    setCategory(oldData.category || "");
    setWhenToComplete(oldData.date ? format(new Date(oldData.date), "yyyy-MM-dd") : null);
    setToDoTask(oldData.task);
    setTaskEmergancy(oldData?.priorityImg || ToDoStatusValue.NO_IMPORTANT);
  }, [oldData]);

  const handleSubmit = () => {
    if (!accId) {
      toast.error("Account Id not found...");
      return;
    }
    const NewData: CreateToDoFormT = {
      _id: accId,
      category: category,
      date: whenToComplete || "",
      priorityImg: taskEmergancy,
      task: todoTask,
      taskId: oldData.taskId,
    };

    mutate(NewData, {
      onSuccess() {
        toast.success("Task updated...");
        onComplete();
      },
      onError(error) {
        toast.error(error.message || "Failed to update task.");
      },
    });
  };

  return (
    <div className="w-full h-full flex flex-col overflow-y-auto no-scrollbar">
      <div className="py-3 flex flex-col w-full gap-4">
        <p className="px-4 font-medium sm:text-base lg:text-lg">
          Update to-do Task{" "}
        </p>
        <Textarea
          value={todoTask}
          onChange={(e) => setToDoTask(e.target.value)}
          placeholder="Type task to-do"
          className="outline-none"
        />
        <div className="flex items-center pl-4 justify-between w-full">
          <p className="text-xs lg:text-sm">How urgent is it?</p>

          <div className="flex items-center gap-0.5">
            <Button
              variant={"ghost"}
              className={cn(
                "w-1 h-5 lg:w-4 lg:h-6 !rounded-sm hover:bg-fb-neutral-300",
                taskEmergancy === ToDoStatusValue.NORMAL && "bg-fb-neutral-400"
              )}
              onClick={() => {
                setTaskEmergancy(ToDoStatusValue.NORMAL);
              }}>
              <Flame className={"size-5 fill-fb-option-7"} strokeWidth={1.3} />
            </Button>
            <Button
              variant={"ghost"}
              className={cn(
                "w-1 h-5 lg:w-4 lg:h-6 !rounded-sm hover:bg-fb-neutral-300",
                taskEmergancy === ToDoStatusValue.NO_IMPORTANT &&
                  "bg-fb-neutral-400"
              )}
              onClick={() => {
                setTaskEmergancy(ToDoStatusValue.NO_IMPORTANT);
              }}>
              <Flame className={"size-5 fill-fb-option-3"} strokeWidth={1.3} />
            </Button>
            <Button
              variant={"ghost"}
              className={cn(
                "w-1 h-5 lg:w-4 lg:h-6 !rounded-sm hover:bg-fb-neutral-300",
                taskEmergancy === ToDoStatusValue.URGENT && "bg-fb-neutral-400"
              )}
              onClick={() => {
                setTaskEmergancy(ToDoStatusValue.URGENT);
              }}>
              <Flame className={"size-5 fill-fb-option-4"} strokeWidth={1.3} />
            </Button>
          </div>
        </div>
        <div className="flex flex-col gap-0.5">
            <label htmlFor="whenComplete " className="px-4 text-xs lg:text-sm">
              When to complete
            </label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={"outline"}
                  className={cn(
                    "h-8 w-full justify-between text-left font-normal",
                    !whenToComplete && "text-muted-foreground"
                  )}
                >
                  {whenToComplete ? (
                    format(whenToComplete, "PPP")
                  ) : (
                    <span>Pick a date</span>
                  )}
                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={whenToComplete ? new Date(whenToComplete) : undefined}
                  onSelect={(date) => setWhenToComplete(date ? format(date, "yyyy-MM-dd") : null)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        <div className="flex flex-col gap-0.5">
          <label id="category" className="px-4 text-xs lg:text-sm">
            Select Category
          </label>

          <Select value={category || ""} onValueChange={setCategory}>
            <SelectTrigger className="h-8 w-full">
              <SelectValue placeholder="Select Category" />
            </SelectTrigger>
            <SelectContent>
              {categorys.map((row, ind) => {
                return (
                  <SelectItem value={row} key={ind}>
                    {row}
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>
        <Button
          className="h-7 w-fit mx-auto text-black bg-fb-warn-100 hover:bg-fb-warn-100/90 text-xs lg:text-sm"
          onClick={onComplete}>
          Cancel
        </Button>
        <Button
          disabled={isPending}
          className="h-7 text-white bg-fb-bPrime-600 hover:bg-fb-bPrime-600/90 text-xs lg:text-sm"
          onClick={handleSubmit}>
          Update Task
        </Button>
      </div>
    </div>
  );
}