import { API_ENDPOINTS } from "../endpoints";
import { API } from "@/utils/axios";
import { handleApiError } from "@/utils/axios/axiosHelper";

// Types for the chatbot request and response
interface ChatbotRequest {
  inputSentence: string;
  userId: string;
  timezone?: string;
  confirm?: boolean;
}

interface ChatbotResponse {
  message: string;
  success: boolean;
  data?: any;
  conflicts?: any[];
  reminderRequired?: boolean;
}

/**
 * @description Processes a message with the ChatbotBUT by calling the API.
 * This function can be used directly within components to send a message.
 * @param payload The chatbot request payload, including the input sentence and userId.
 * @returns A promise that resolves to the chatbot's response.
 */
export const processMessageBUT = async (
  payload: ChatbotRequest
): Promise<ChatbotResponse> => {
  const failureMessage = "Failed to get response from the chatbot...";

  try {
    // Make the POST request to the chatbot endpoint
    const response = await API.post<ChatbotResponse>(
      API_ENDPOINTS.ai.chatbotEni,
      payload
    );
    return response.data;
  } catch (error) {
    // Re-throw a structured error using the error handling helper
    throw handleApiError(error, failureMessage);
  }
};