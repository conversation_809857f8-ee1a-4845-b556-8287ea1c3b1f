import { isToday, is<PERSON><PERSON>d, parse<PERSON><PERSON> } from "date-fns";

// Ensure today is included even if empty
// const todayKey = new Date().toDateString();
// if (!groupedMap.has(todayKey)) {
//   groupedMap.set(todayKey, { todos: [], events: [] });
// }
import { Dispatch, SetStateAction, useMemo, useState } from "react";

import { Button } from "@/components/ui/button";
import { EventModelT, TodoListModelT } from "@/types";
import {
  filterOngoingMonthEvents,
  filterOngoingMonthTodos,
  formatEventDate,
} from "@/utils";
import MoodNoteBlue from "@/components/common/mood-note-blue";
import AddEventSmallView from "@/components/common/add-event-small";
import { useCalendarsWithEvents } from "@/hook/useCalendarsWithEvents";
import {
  EventMiniCardView,
  TodoCardView,
} from "@/components/common/event/card-view";
import { EventMonth<PERSON>hangeComponent } from "@/components/common/event/date-change-comp";
import { useAuth } from "@/context/AuthContext";

type GroupedModelT = {
  date: string;
  todos: TodoListModelT[];
  events: EventModelT[];
};

const groupDataByDate = (
  events: EventModelT[],
  todos: TodoListModelT[]
): GroupedModelT[] => {
  const groupedMap = new Map<
    string,
    { todos: TodoListModelT[]; events: EventModelT[] }
  >();

  // Group todos
  for (const todo of todos) {
    const parsedDate = parseISO(todo.date);
    if (!isValid(parsedDate)) {
      console.warn("Skipping invalid todo date:", todo.date);
      continue;
    }

    const dateKey = parsedDate.toDateString();
    if (!groupedMap.has(dateKey)) {
      groupedMap.set(dateKey, { todos: [], events: [] });
    }
    groupedMap.get(dateKey)!.todos.push(todo);
  }

  // Group events
  for (const event of events) {
    const parsedDate = parseISO(event.start);
    if (!isValid(parsedDate)) {
      console.warn("Skipping invalid event date:", event.start);
      continue;
    }

    const dateKey = parsedDate.toDateString();
    if (!groupedMap.has(dateKey)) {
      groupedMap.set(dateKey, { todos: [], events: [] });
    }
    groupedMap.get(dateKey)!.events.push(event);
  }

  // Convert Map to sorted array
  const groupedArray = Array.from(groupedMap.entries())
    .map(([date, { todos, events }]) => ({
      date,
      todos,
      events,
    }))
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  return groupedArray;
};

export default function EventsOnMonthView({
  date,
  setDate,
}: {
  date: Date;
  setDate: Dispatch<SetStateAction<Date>>;
}) {
  const { userId } = useAuth();

  const { events, TodoList } = useCalendarsWithEvents(userId);

  const [isAddEvent, setIsAddEvent] = useState(false);

  const { filterEventsData, filterTodosData } = useMemo(() => {
    const filterEventsData = filterOngoingMonthEvents(events, date);

    const filterTodosData = filterOngoingMonthTodos(TodoList, date);

    return { filterEventsData, filterTodosData };
  }, [events, date, TodoList]);

  const combileTodoAndEvent = groupDataByDate(
    filterEventsData,
    filterTodosData
  );

  return !isAddEvent ? (
    <div className="flex flex-col gap-2 w-full overflow-y-auto no-scrollbar h-full">
      {/* date */}
      <EventMonthChangeComponent date={date} setDate={setDate} />

      {/* events */}
      {filterEventsData.length === 0 && filterTodosData.length === 0 ? (
        <div className="flex flex-1 flex-col gap-2 justify-center items-center px-5">
          <MoodNoteBlue text="You do not have any events yet" />
          <Button
            className="bg-fb-bPrime-600 h-8 w-full"
            onClick={() => setIsAddEvent(true)}>
            Create One
          </Button>
        </div>
      ) : (
        <div className="flex flex-col gap-3 ">
          {combileTodoAndEvent.map((row, index) => {
            const rawDate = new Date(row.date);
            const isCurrentDay = isToday(rawDate);
            const date = formatEventDate(row.date);

            return (
              <div key={index} className="flex flex-col gap-2 mb-1">
                <div className="flex gap-1 items-center justify-between border-b border-fb-bPrime-hgts px-2">
                  <div className="flex gap-1 items-end">
                    <p className="font-semibold text-base">{date.date},</p>
                    <p className="font-light text-xs">({date.day})</p>
                  </div>
                  {isCurrentDay && (
                    <p className="font-semibold text-sm">Today</p>
                  )}
                </div>

                {row.todos.map((item, index) => {
                  return <TodoCardView {...item} key={index} />;
                })}
                {row.events.map((item, index) => {
                  return <EventMiniCardView {...item} key={index} />;
                })}
              </div>
            );
          })}
        </div>
      )}
    </div>
  ) : (
    <AddEventSmallView onCloseMainView={() => setIsAddEvent(false)} />
  );
}
