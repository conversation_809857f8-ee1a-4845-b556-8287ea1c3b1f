import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

import { API_ENDPOINTS } from "../endpoints";

import { API } from "@/utils/axios";
import { StatusCodes } from "@/types/api";
import { AccountDataT, UserAccountResponseT } from "@/types";
import { handleApiError, handleApiResponse } from "@/utils/axios/axiosHelper";
import { Query_Key } from "@/constant/data";
import { Api_Upload_Image } from "../helper";

export const useGetAccounDetails = (payload: { userId?: string | null }) => {
  return useQuery<UserAccountResponseT>({
    queryKey: [Query_Key.accountDetails],
    queryFn: () => Api_Get_Account_Details(payload.userId),
    refetchInterval: 10000, // Auto refresh every 10 seconds
    staleTime: 600000, // Keeps data fresh for 60 min before refetching
    enabled: !!payload.userId, // ⛔ won't run unless calendarIds is truthy
  });
};

export const useUpdateAccountDetails = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: Api_Update_Account_Details,
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: [Query_Key.accountDetails] });
    },
  });
};

export const useCreateAccount = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: Api_Create_Account,
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: [Query_Key.accountDetails] });
    },
  });
};

const Api_Get_Account_Details = async (
  userId?: string | null
): Promise<UserAccountResponseT> => {
  const FailureMessage = "Failed to get account details...";

  const data = { _id: userId };

  try {
    const response = await API.post(API_ENDPOINTS.account.get, data);
    const result = handleApiResponse<UserAccountResponseT>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success || !result.data) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_Update_Account_Details = async (
  data: AccountDataT
): Promise<null> => {
  const FailureMessage = "Failed to update acoount...";

  const { pictureImage, ...rest } = data;

  try {
    let imageUri = rest.picture;
    if (pictureImage) {
      console.log("----update profile image---", pictureImage);
      imageUri = await Api_Upload_Image(pictureImage);
      rest.picture = imageUri;
    }

    rest.picture = imageUri;

    const response = await API.post(API_ENDPOINTS.account.update, rest);
    const result = handleApiResponse<null>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_Create_Account = async (data: AccountDataT): Promise<null> => {
  const FailureMessage = "Failed to create account...";
  const { pictureImage, ...rest } = data;

  try {
    let imageUri = rest.picture;
    if (pictureImage) {
      console.log("----update profile image---", pictureImage);
      imageUri = await Api_Upload_Image(pictureImage);
      rest.picture = imageUri;
    }

    rest.picture = imageUri;
    const response = await API.post(API_ENDPOINTS.account.create, rest);
    const result = handleApiResponse<null>(
      response,
      StatusCodes.CREATED,
      FailureMessage
    );

    if (!result.success) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};
