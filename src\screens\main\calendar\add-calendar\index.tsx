import { toast } from "sonner";
import { FormEvent, useState } from "react";
import { useNavigate } from "react-router";
import { ChevronLeft, X } from "lucide-react";

import { cn } from "@/lib/utils";
import { generateUUID } from "@/utils";
import { FileWithPreview } from "@/types";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { CreatCalendarFormT } from "@/types/api";
import { useCreateCalendar } from "@/api/calendar";
import { Textarea } from "@/components/ui/textarea";
import GellaryIcon from "@/assets/svg/image-icon.svg";
import InfoIcon from "@/assets/svg/Info-circle-icon.svg";
import BussinessInfo from "@/components/common/bussiness-info";
import { ConformationPopup } from "@/components/common/conformation-popup";
import { useAuth } from "@/context/AuthContext";
import { useGetAccounDetails } from "@/api/account";
import { useCustomDropzone, FILE_TYPES } from "@/hook/useCustomDropzone";

export default function AddCalendarScreen() {
  const navigate = useNavigate();
  const { mutate, isPending } = useCreateCalendar();

  const { userId } = useAuth();

  const { data: AccountData } = useGetAccounDetails({
    userId: userId,
  });

  const [calendarImage, setCalendarImage] = useState<FileWithPreview[]>([]);
  const [uniqId, setUniqId] = useState("");
  const [calendarName, setCalendarName] = useState("");
  const [description, setDescription] = useState("");
  const [isPrivate, setIsPrivate] = useState(false);

  const [conformationPopup, setConformationPopup] = useState({
    open: false,
    title: "",
    subTitle: "",
  });

  const [errors, setErrors] = useState({ calendarName: false, uniqId: false });

  const { getRootProps, getInputProps } = useCustomDropzone({
    acceptedFileTypes: FILE_TYPES.IMAGES,
    maxFiles: 1,
    maxSize: 1 * 1024 * 1024, // 1MB
    multiple: false,
    showToastOnError: true,
    customErrorMessages: {
      "file-invalid-type": "Please upload only image files for calendar cover",
      "file-too-large": "Calendar image is too large",
      "too-many-files": "You can only upload one calendar image",
    },
    onFilesAccepted: (acceptedFiles) => {
      setCalendarImage(
        acceptedFiles.map((file) =>
          Object.assign(file, {
            preview: URL.createObjectURL(file),
          })
        )
      );
    },
    // Note: No onFilesRejected callback - errors will be handled automatically by the hook
  });

  const CalerAllData = () => {
    setCalendarImage([]);
    setUniqId("");
    setCalendarName("");
    setDescription("");
    setErrors({ calendarName: false, uniqId: false });
  };

  const handleCreateCalendar = (e: FormEvent) => {
    e.preventDefault();
    if (!uniqId && !calendarName) {
      setErrors({ calendarName: true, uniqId: true });
      return;
    } else if (!uniqId) {
      setErrors((old) => ({ ...old, uniqId: true }));
      return;
    } else if (!calendarName) {
      setErrors((old) => ({ ...old, calendarName: true }));
      return;
    }

    const FormData: CreatCalendarFormT = {
      businessName: "Fit X Gym",
      _id: generateUUID("bizCal"),
      businessId: AccountData?.accData?.[0]?.email || "",
      bizId: generateUUID("bizAcc"),
      calendarName: calendarName,
      calendarId: uniqId,
      phoneNumber: "",
      email: AccountData?.accData?.[0]?.email || "",
      deleted: false,
      followers: [],
      businessUser: true,
      private: isPrivate,
      blocked: false,
      catagory: "",
      subCatagory: "",
      usrId: userId || "",
      picture: null,
      pictureImage: calendarImage[0] || null,
      description: description,
    };

    mutate(FormData, {
      onSuccess() {
        handleSetConformation({
          title: "Calendar created",
          subTitle: "",
        });
        CalerAllData();
      },
      onError(error) {
        console.log("Error object:", error);
        if (error.message.includes("Calendar with this ID already exists")) {
          setErrors((old) => ({ ...old, uniqId: true }));
        } else {
          toast.error(error.message);
        }
      },
    });
  };

  const handleSetConformation = ({
    title,
    subTitle,
  }: {
    title: string;
    subTitle: string;
  }) => {
    setConformationPopup({ open: true, title: title, subTitle: subTitle });
  };

  const handleClosePopups = () => {
    setConformationPopup({ open: false, title: "", subTitle: "" });
  };

  return (
    <div className="flex flex-col w-full h-dvh ">
      <div className="flex justify-between">
        {/* screen name */}
        <div className="flex w-full px-6">
          <p className="text-base lg:text-xl mt-auto">Create a Calendar</p>
        </div>
        <div className="w-52 lg:w-72 ml-auto pt-2">
          <BussinessInfo />
        </div>
      </div>
      <div className="flex flex-col gap-2 w-full h-[calc(100dvh-40px)] lg:h-[calc(100dvh-48px)] pb-3 pr-3  mt-2">
        <form
          onSubmit={handleCreateCalendar}
          className="bg-white w-full h-full overflow-hidden flex flex-col p-4 lg:p-6 gap-4 rounded-2xl overflow-y-auto no-scrollbar">
          {/* calendar details */}
          <div>
            <Button
              variant={"ghost"}
              className="h-6 lg:h-8 text-sm lg:text-base text-fb-warn-600"
              onClick={() => navigate(-1)}>
              <ChevronLeft className=" lg:!size-6 text-black" />
              Back to calendars
            </Button>
          </div>
          <div>
            <p className="font-semibold text-xl lg:text-2xl pl-3">
              New Calendar
            </p>
          </div>
          <div className="flex flex-col md:flex-row gap-4 md:gap-6 w-full">
            <div className="flex gap-2 flex-col justify-between w-full">
              <div className="flex flex-col gap-0.5 w-full">
                <Label
                  htmlFor="calendar-name"
                  className="text-black text-base font-normal px-4 flex gap-0.5 text-center">
                  Calendar Name*
                </Label>
                <Input
                  id="calendar-name"
                  placeholder="Calendar name"
                  value={calendarName}
                  onChange={(e) => {
                    setCalendarName(e.target.value);
                    setErrors((old) => ({ ...old, calendarName: false }));
                  }}
                  className={cn(errors.calendarName && "border-fb-warn-500")}
                />
                {errors.calendarName && (
                  <p className="text-xs text-fb-warn-500 font-medium px-4">
                    Enter calendar name
                  </p>
                )}
              </div>
              <div className="flex flex-col gap-0.5 w-full">
                <Label
                  htmlFor="uniq-id"
                  className="text-black text-base font-normal px-4 flex gap-0.5 text-center">
                  Choose an unique id*
                  <img src={InfoIcon} alt="" className="w-4 h-4" />
                </Label>
                <Input
                  id="uniq-id"
                  placeholder="@bussinessid_"
                  value={uniqId}
                  onChange={(e) => {
                    setUniqId(e.target.value);
                    setErrors((old) => ({ ...old, uniqId: false }));
                  }}
                  className={cn(errors.uniqId && "border-fb-warn-500")}
                />
                {errors.uniqId && (
                  <p className="text-xs text-fb-warn-500 font-medium px-4">
                    Enter calendar id
                  </p>
                )}
              </div>
              <div className="flex flex-col w-full">
                <Label
                  htmlFor="Description"
                  className="text-black text-base font-normal px-4 flex gap-0.5 text-center">
                  Description
                </Label>
                <Textarea
                  id="Description"
                  placeholder="What is this calendar for?"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                />
              </div>
            </div>
            <div className="flex flex-col w-full">
              <p className="text-base text-black px-4">Choose an Image</p>
              {calendarImage.length > 0 ? (
                <div className="w-full h-56 rounded-xl overflow-hidden relative bg-fb-neutral-200">
                  <img
                    src={calendarImage[0]?.preview}
                    alt=""
                    className="w-full h-full object-contain rounded-xl"
                  />
                  <Button
                    variant={"ghost"}
                    size={"icon"}
                    className="absolute top-2 right-2 h-6 w-6"
                    onClick={() => setCalendarImage([])}>
                    <X className="text-fb-warn-600" />
                  </Button>
                </div>
              ) : (
                <div
                  {...getRootProps({ className: "dropzone" })}
                  className="bg-fb-neutral-200 rounded-xl h-56 border border-black flex justify-center items-center cursor-pointer">
                  <input {...getInputProps()} />
                  <img src={GellaryIcon} className="h-28" />
                </div>
              )}
            </div>
          </div>

          <div className="flex gap-6 items-center">
            <div className="w-full flex px-3 items-center gap-2">
              <Label
                htmlFor="keep-Private"
                className="text-black text-base font-normal cursor-pointer flex gap-0.5 text-center">
                Keep it Private
                <img src={InfoIcon} alt="" className="w-4 h-4" />
              </Label>
              <Switch
                checked={isPrivate}
                onCheckedChange={() => setIsPrivate(!isPrivate)}
                id="keep-Private"
                size="md"
              />
            </div>
          </div>
          <div className="flex justify-center ">
            <Button
              disabled={isPending}
              className="bg-fb-bPrime-600 rounded-full h-9 w-64 "
              onClick={handleCreateCalendar}>
              Create Calendar
            </Button>
          </div>
        </form>
        <ConformationPopup
          isOpen={conformationPopup.open}
          title={conformationPopup.title}
          subTitle={conformationPopup.subTitle}
          onCLose={() => {
            handleClosePopups();
            navigate(-1);
          }}
        />
      </div>
    </div>
  );
}
