import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { API_ENDPOINTS } from "../endpoints";
import { API } from "@/utils/axios";
import { CalendarModelT } from "@/types";
import { CreatCalendarFormT, StatusCodes } from "@/types/api";
import { handleApiError, handleApiResponse } from "@/utils/axios/axiosHelper";
import { Query_Key } from "@/constant/data";
import { Api_Upload_Image } from "../helper";

// Custom error class for calendar-specific errors
export class CalendarError extends Error {
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'CalendarError';
  }
}

export const useGetAllCalendar = (payload: { usrId?: string | null }) => {
  return useQuery<CalendarModelT[]>({
    queryKey: [Query_Key.allCalendar],
    queryFn: () => Api_Get_Calendars(payload),
    refetchInterval: 10000,
    staleTime: 60000,
    enabled: !!payload.usrId,
  });
};

export const useGetCalendarDetails = (payload: { calendarIds?: string[] }) => {
  return useQuery<CalendarModelT[]>({
    queryKey: [Query_Key.calendarDetails, payload.calendarIds],
    queryFn: () => Api_Get_Calendar_Details(payload),
    refetchInterval: 30000,
    staleTime: 60000,
    enabled: payload.calendarIds && payload.calendarIds.length > 0,
  });
};

export const useUpdateCalendar = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: Api_Update_Calendar,
    onSuccess(_, { _id }) {
      queryClient.invalidateQueries({ queryKey: [Query_Key.allCalendar] });
      queryClient.invalidateQueries({
        queryKey: [Query_Key.calendarDetails, [_id]],
      });
    },
  });
};

export const useCreateCalendar = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: Api_Create_Calendar,
    onSuccess(data) {
      queryClient.invalidateQueries({ queryKey: [Query_Key.allCalendar] });
      queryClient.invalidateQueries({
        queryKey: [Query_Key.calendarDetails, [data._id]],
      });
    },
    onError: (error) => {
      // You can handle specific error types here if needed
      console.error('Calendar creation failed:', error);
    }
  });
};

const Api_Get_Calendars = async (payload: {
  usrId?: string | null;
}): Promise<CalendarModelT[]> => {
  const FailureMessage = "Failed to get calendars...";

  try {
    const response = await API.post(API_ENDPOINTS.calendar.getAll, payload);
    const result = handleApiResponse<CalendarModelT[]>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success || !result.data) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw handleApiError(error, FailureMessage);
  }
};

const Api_Get_Calendar_Details = async ({
  calendarIds,
}: {
  calendarIds?: string[];
}): Promise<CalendarModelT[]> => {
  const FailureMessage = "Failed to get calendar...";

  try {
    const response = await API.post(
      API_ENDPOINTS.calendar.singleOne,
      calendarIds
    );
    const result = handleApiResponse<CalendarModelT[]>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success || !result.data) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw handleApiError(error, FailureMessage);
  }
};

// Enhanced validation function
const validateCalendarId = async (calendarId: string, usrId: string): Promise<void> => {
  try {
    const existingCalendars = await Api_Get_Calendars({ usrId });
    const isDuplicate = existingCalendars.some(
      (calendar) => calendar.calendarId === calendarId
    );

    if (isDuplicate) {
      throw new CalendarError(
        "Calendar ID already exists. Please choose a unique ID.",
        "DUPLICATE_CALENDAR_ID"
      );
    }
  } catch (error) {
    // If it's already a CalendarError, re-throw it
    if (error instanceof CalendarError) {
      throw error;
    }
    // For other errors (like network issues), throw a generic validation error
    throw new CalendarError(
      "Unable to validate calendar ID. Please try again.",
      "VALIDATION_ERROR"
    );
  }
};

const Api_Create_Calendar = async (
  data: CreatCalendarFormT
): Promise<{ _id: string }> => {
  const { pictureImage, ...rest } = data;

  try {
    // Validate calendar ID before proceeding
    await validateCalendarId(rest.calendarId, rest.usrId);

    // Upload image
    const imageUri = await Api_Upload_Image(pictureImage);
    rest.picture = imageUri;

    // Create calendar
    const response = await API.post(API_ENDPOINTS.calendar.create, rest);
    const result = handleApiResponse<{ _id: string }>(
      response,
      StatusCodes.CREATED,
      "Failed to create calendar..."
    );

    if (!result.success || !result.data) {
      throw new CalendarError(
        result.message || "Failed to create calendar...",
        "CREATION_FAILED"
      );
    }
    
    return result.data;
  } catch (error) {
    // If it's already a CalendarError, re-throw it to preserve the specific message
    if (error instanceof CalendarError) {
      throw error;
    }
    
    // For other errors, wrap them appropriately
    throw handleApiError(error, "Failed to create calendar...");
  }
};

const Api_Update_Calendar = async (data: CreatCalendarFormT): Promise<null> => {
  const FailureMessage = "Failed to update calendar...";
  const { pictureImage, ...rest } = data;

  try {
    let imageUri = rest.picture;
    if (pictureImage) {
      console.log("----update calendar image---", pictureImage);
      imageUri = await Api_Upload_Image(pictureImage);
      rest.picture = imageUri;
    }

    const response = await API.put(API_ENDPOINTS.calendar.update, rest, {
      headers: {
        "Content-Type": "application/json",
      },
    });

    const result = handleApiResponse<null>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw handleApiError(error, FailureMessage);
  }
};
