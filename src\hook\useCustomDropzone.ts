import {
  useDropzone,
  DropzoneOptions,
  FileRejection,
  FileWithPath,
} from "react-dropzone";
import { toast } from "sonner";

// Default file type configurations
export const FILE_TYPES = {
  EXCEL: {
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
      [] as string[], // .xlsx
    "application/vnd.ms-excel": [] as string[], // .xls
    "text/csv": [] as string[], // .csv
  },
  IMAGES: {
    "image/*": [".jpg", ".jpeg", ".png", ".gif", ".webp", ".avif"],
  },
  DOCUMENTS: {
    "application/pdf": [".pdf"],
    "application/msword": [".doc"],
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [
      ".docx",
    ],
  },
  ALL_FILES: {
    "*": [] as string[],
  },
};

// Default error messages
export const DEFAULT_ERROR_MESSAGES = {
  "file-invalid-type": "Please upload valid files",
  "file-too-large": "File size is too large",
  "too-many-files": "Too many files selected",
  default: "Something went wrong",
} as const;

export interface UseCustomDropzoneProps {
  // File configuration
  acceptedFileTypes?: Record<string, string[]>;
  maxFiles?: number;
  maxSize?: number; // in bytes
  multiple?: boolean;

  // Callbacks
  onFilesAccepted?: (files: FileWithPath[]) => void;
  onFilesRejected?: (fileRejections: FileRejection[]) => void;

  // Error handling
  showToastOnError?: boolean;
  customErrorMessages?: Partial<
    Record<keyof typeof DEFAULT_ERROR_MESSAGES | string, string>
  >;

  // Additional dropzone options
  additionalOptions?: Partial<DropzoneOptions>;
}

export interface UseCustomDropzoneReturn {
  getRootProps: ReturnType<typeof useDropzone>["getRootProps"];
  getInputProps: ReturnType<typeof useDropzone>["getInputProps"];
  isDragActive: boolean;
  isDragAccept: boolean;
  isDragReject: boolean;
  acceptedFiles: readonly FileWithPath[];
  fileRejections: readonly FileRejection[];
}

export const useCustomDropzone = ({
  acceptedFileTypes = FILE_TYPES.EXCEL,
  maxFiles = 1,
  maxSize = 1 * 1024 * 1024, // 1MB default
  multiple = false,
  onFilesAccepted,
  onFilesRejected,
  showToastOnError = true,
  customErrorMessages = {},
  additionalOptions = {},
}: UseCustomDropzoneProps = {}): UseCustomDropzoneReturn => {
  const errorMessages = { ...DEFAULT_ERROR_MESSAGES, ...customErrorMessages };

  const handleFilesAccepted = (acceptedFiles: FileWithPath[]) => {
    if (onFilesAccepted) {
      onFilesAccepted(acceptedFiles);
    }
  };

  const handleFilesRejected = (fileRejections: FileRejection[]) => {
    // Always handle errors with toast notifications if enabled
    if (showToastOnError && fileRejections.length > 0) {
      const firstError = fileRejections[0].errors[0];
      const errorCode = firstError.code as keyof typeof DEFAULT_ERROR_MESSAGES;
      const message = errorMessages[errorCode] || errorMessages.default;

      // Customize message based on error type
      if (errorCode === "file-too-large") {
        const sizeMB = (maxSize / (1024 * 1024)).toFixed(1);
        toast.error(`${message}. Maximum size allowed is ${sizeMB}MB`);
      } else if (errorCode === "too-many-files") {
        toast.error(
          `${message}. You can only upload ${maxFiles} file${
            maxFiles > 1 ? "s" : ""
          }`
        );
      } else if (errorCode === "file-invalid-type") {
        toast.error(`${message}. Please check the file format.`);
      } else {
        toast.error(message);
      }
    }

    // Call custom callback if provided
    if (onFilesRejected) {
      onFilesRejected(fileRejections);
    }

    // Log errors for debugging (optional)
    if (fileRejections.length > 0) {
      console.warn(
        "File upload rejected:",
        fileRejections.map((rejection) => ({
          fileName: rejection.file.name,
          errors: rejection.errors.map((error) => ({
            code: error.code,
            message: error.message,
          })),
        }))
      );
    }
  };

  const {
    getRootProps,
    getInputProps,
    isDragActive,
    isDragAccept,
    isDragReject,
    acceptedFiles,
    fileRejections,
  } = useDropzone({
    accept: acceptedFileTypes,
    maxFiles,
    maxSize,
    multiple,
    onDrop: handleFilesAccepted,
    onDropRejected: handleFilesRejected,
    ...additionalOptions,
  });

  return {
    getRootProps,
    getInputProps,
    isDragActive,
    isDragAccept,
    isDragReject,
    acceptedFiles,
    fileRejections,
  };
};

// Utility function to format file size
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// Utility function to get file extension
export const getFileExtension = (filename: string): string => {
  return filename.slice(((filename.lastIndexOf(".") - 1) >>> 0) + 2);
};
