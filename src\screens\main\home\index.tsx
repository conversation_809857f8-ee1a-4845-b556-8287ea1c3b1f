import { useEffect, useState } from "react";

import MoodNoteBlue from "@/components/common/mood-note-blue";
import YearCalendar from "@/components/calendar/year-calendar";
import BussinessInfo from "@/components/common/bussiness-info";
import MonthCalendar from "@/components/calendar/month-calendar";
import WeeklyCalendar from "@/components/calendar/weekly-calendar";
// import SideCalendarView from "@/components/common/side-calendar-view";
import CalendarTabSwitcher, {
  AvailableCalendarTabs,
} from "@/components/calendar/calendar-tab-switch";
import UpComingEvents from "@/components/calendar/upcoming-events";
import EventsOnDateView from "@/components/common/event/events-onDate";
import { useCalendarsWithEvents } from "@/hook/useCalendarsWithEvents";

import { useSearchParams } from "react-router";
import { useAuth } from "@/context/AuthContext";
import AppLoader from "@/components/common/app-loader";
import AiChat from "../../../components/ai/AiChat";
import EniIcon from "@/assets/images/AI Button.png";

export default function HomeScreen() {
  const [searchParams, setSearchParams] = useSearchParams();

  const { userId } = useAuth();

  
  const [isAiChatOpen, setIsAiChatOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());

  const initialTab =
    (searchParams.get("tab") as AvailableCalendarTabs) || "Upcoming";
  const [activeTab, setActiveTab] = useState<AvailableCalendarTabs>(initialTab);

  const { events, loading, upComingEvents, TodoList } =
    useCalendarsWithEvents(userId);

  useEffect(() => {
    // Sync state with URL when tab changes
    setSearchParams({ tab: activeTab });
  }, [activeTab, setSearchParams]);

  const handleTabChange = (tab: AvailableCalendarTabs) => {
    setActiveTab(tab);
  };

  const handleAiChatToggle = () => {
    setIsAiChatOpen(!isAiChatOpen);
  };

  if (loading) return <AppLoader />;

  return (
    <div className="relative flex w-full h-dvh">
      <div className="relative overflow-hidden flex flex-col gap-2 py-3 w-[calc(100%-250px)] h-full">
        <CalendarTabSwitcher
          defaultTab={activeTab}
          onTabChange={handleTabChange}
        />

        {activeTab === "Upcoming" && (
          <UpComingEvents events={upComingEvents} TodoList={TodoList} calendar={null} />
        )}
        {activeTab === "Week" && (
          <WeeklyCalendar events={events} Todos={TodoList} currentDate={selectedDate} onDateChange={setSelectedDate} />
        )}
        {activeTab === "Month" && <MonthCalendar events={events} Todos={TodoList} currentDate={selectedDate} setCurrentDate={setSelectedDate} />}
        {activeTab === "Year" && (
          <YearCalendar
            currentYear={selectedDate}
            setCurrentYear={setSelectedDate}
            onClick={setSelectedDate}
          />
        )}
      </div>
      
      {/* events side data */}
      <div className="relative w-96 lg:w-[450px] overflow-hidden">
        {/* Sidebar content */}
        <div 
          className={`absolute inset-0 px-4 py-3 flex flex-col gap-3 transition-transform duration-500 ease-in-out ${
            isAiChatOpen ? 'transform translate-x-full' : 'transform translate-x-0'
          }`}
        >
          {/* bussiness details */}
          <BussinessInfo />
          <div className="flex w-full h-[calc(100dvh-66px)] lg:h-[calc(100dvh-72px)] justify-center items-center">
            {activeTab === "Upcoming" ? (
              <MoodNoteBlue text="Select an event to see details" />
            ) : activeTab === "Year" ? (
              <EventsOnDateView selectedDate={selectedDate} />
            ) : (
              <EventsOnDateView selectedDate={selectedDate} />
            )}
          </div>

          {/* calendar view
          {activeTab === "Upcoming" && <SideCalendarView />} */}
        </div>

        {/* AI Chat */}
        <div 
          className={`absolute inset-0 transition-transform duration-500 ease-in-out ${
            isAiChatOpen ? 'transform translate-x-0' : 'transform translate-x-full'
          }`}
        >
          <AiChat onClose={handleAiChatToggle} />
        </div>
      </div>

      {/* AI Button - positioned at bottom right of entire screen */}
      {!isAiChatOpen && (
        <button
          onClick={handleAiChatToggle}
          className="fixed bottom-6 right-6 z-50 p-0 bg-transparent border-none cursor-pointer transition-transform duration-300 hover:scale-110"
        >
          <img src={EniIcon} alt="AI Assistant" className="w-20 h-20" />
        </button>
      )}
    </div>
  );
}