import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  build: {
    outDir: 'dist/client',
  },
  server: {
    proxy: {
      "/api": {
        // target: "https://fragment.thefragment.app",
        // target: "http://localhost:3000",
        target: process.env.VITE_BASE_URL,
        changeOrigin: true,
        secure: true, // Set to false if using self-signed certs
        // rewrite: (path) => path.replace(/^\/api/, ""),
      },
    },
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
});