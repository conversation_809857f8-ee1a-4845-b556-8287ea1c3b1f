import { ToDoStatusValue } from "@/enums";
import { IconDefinition } from "@fortawesome/free-solid-svg-icons";

export type NavRouteT = {
  icon: IconDefinition | string;
  label: string;
  path: string;
  isIcon: boolean;
};

export interface FileWithPreview extends File {
  preview: string;
}

export type AgendaItem = {
  agendaId: string;
  type: string;
  timestamp: string;
  action: string;
  under: string;
  date: string;
  userId: string;
  isReadBy?: string[];
};

export type ZoneInfo = {
  label: string;
  tzCode: string;
  name: string;
  utc: string;
};

export type EventModelT = {
  _id: string;
  businessName: string;
  calendarName: string;
  calId: string;
  logo?: string;
  title: string;
  allDayEvent: boolean;
  start: string;
  end: string;
  repeat: boolean;
  frequency: string;
  repeatEvery: number;
  days: string[];
  repeatEndDate: string;
  location: string;
  link: string[];
  picture?: string;
  profileImage: File | null;
  youtube: string;
  restrict: string[];
  zone: ZoneInfo;
  attach: string;
  description: string;
  reminderSelected: string;
  reminder: string;
  personalReminder: string;
  notifyBefore: string;
  event: boolean;
  note: boolean;
  color: string;
  userType: string;
  textColor: string;
  deleted: boolean;
  blocked: boolean;
  createdAt: string;
  Impression: []; // any
  Rsvps: []; // any
  Comments: CommentModelT[]; // any
  Reply: SubCommentModelT[]; // any
  usrId: string;
  Agenda: AgendaItem[];
};

// Single follower
export type FollowerT = {
  usrId: string;
  displayName: string;
  email: string | null;
  userId: string;
  picture: string | null;
};

// Follow history
export type FollowHistoryT = {
  usrId: string;
  date: string; // ISO date string
  action: string;
};

// Calendar model
export type CalendarModelT = {
  _id: string;
  businessName: string;
  businessId: string;
  bizId: string;
  calendarName: string;
  calendarId: string;
  phoneNumber: string;
  email: string | null;
  deleted: boolean;
  followers: FollowerT[];
  businessUser: boolean;
  private: boolean;
  blocked: boolean;
  catagory: string;
  subCatagory: string;
  description: string;
  usrId: string;
  createdAt: string;
  isRead?: boolean;
  followHistory?: FollowHistoryT[];
  picture?: string | null;
};

export type UserAccountResponseT = {
  userData: UserDataT;
  accData: AccountDataT[];
};

export type UserDataT = {
  _id: string;
  isAnonymous: boolean;
  displayName: string;
  email: string | null;
  uid: string;
  providerId: string;
  blocked: boolean;
  businessUser: boolean;
  fcmToken: string;
  deleted: boolean;
  type: "businessUser" | "clientUser" | string; // (if you have more types, add here)
  createdAt: string;
  timezone: string;
  deviceId: string;
  deviceBrand: string;
  deviceBuildNumber: string;
  deviceBundleId: string;
  deviceDeviceType: string;
  deviceSystemName: string;
  deviceSystemVersion: string;
  version: string;
  isTablet: boolean;
  reportCount: number;
  reports: ReportItemT[];
};

export type ReportItemT = {
  userId: string;
  message: string;
};

export type AccountDataT = {
  _id: string;
  businessName: string;
  businessId: string;
  picture?: string;
  website: string;
  location: string;
  email: string;
  instagram: string;
  linkedIn: string;
  deleted: boolean;
  blocked: boolean;
  description: string;
  usrId: string;
  Tasks?: TodoListModelT[];
  Category?: string[];
  pictureImage?: File;
};

export type TodoListModelT = {
  taskId: string;
  category: string;
  priorityImg: ToDoStatusValue;
  task: string;
  createdAt: string;
  date: string;
  status?: ToDoStatusValue;
  updatedAt?: string;
  comment?: string;
};

export type CommentModelT = {
  commentId: string;
  userId: string;
  displayName: string;
  date: string;
  comment: string;
  timeStmp: string;
  type: string;
  picture: string | null;
  report?: boolean;
  reportCount?: number;
  reportReasons?: string[];
  reportedBy?: string[];
};

export type SubCommentModelT = {
  replyId: string;
  commentId: string;
  userId: string;
  displayName: string;
  date: string;
  reply: string;
  timeStmp: string;
  type: string;
  picture: string | null;
  report?: boolean;
  reportCount?: number;
  reportReasons?: string[];
  reportedBy?: string[];
};

export type UploadImageResponseT = {
  location: string;
  publicUrl: string;
  key: string;
  mimetype: string;
  size: number;
  sizeInMB: string;
  originalName: string;
  uploadedAt: string;
};

export type SignUpResT = {
  user: UserDataT;
  account: AccountDataT;
};
