import { CalendarModelT, EventModelT, TodoListModelT } from "@/types";
import BgImage from "@/assets/images/bg-image.png";
import DateGroupEventList from "@/components/common/event/date-wise-group-events";

export default function UpComingEvents({
  events,
  TodoList,
  calendar,
  isPublicView = false,
}: {
  events: EventModelT[];
  TodoList: TodoListModelT[];
  calendar: CalendarModelT | null;
  isPublicView?: boolean;
}) {
  const filteredEvents = events.filter((event) => {
    if (!event.restrict || event.restrict.length === 0) {
      return true; // Keep event if restrict is empty or doesn't exist
    }
    const eventStartDate = new Date(event.start).toISOString().split("T")[0]; // YYYY-MM-DD
    const restrictedDates = event.restrict.map(
      (d) => new Date(d).toISOString().split("T")[0]
    );
    return !restrictedDates.includes(eventStartDate);
  });

  return (
    <div className="overflow-hidden flex flex-col h-full">
      {/* Calendar Header */}
      <div className="flex items-center mx-auto gap-2 w-fit">
        <h2 className="text-sm font-semibold">Upcoming Events</h2>
      </div>
      <div className="relative overflow-auto flex-1 bg-white rounded-12px">
        <img
          className="w-full h-full absolute inset-0 opacity-20"
          src={BgImage}
          alt=""
        />
        <div className="relative  w-full h-full flex flex-col p-6  overflow-y-auto no-scrollbar z-10">
          <DateGroupEventList
            events={filteredEvents}
            Todos={TodoList}
            calendar={calendar}
            isPublicView={isPublicView}
          />
        </div>
      </div>
    </div>
  );
}
