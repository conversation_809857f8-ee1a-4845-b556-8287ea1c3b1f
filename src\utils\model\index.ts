import { EventModelT } from "@/types";
import { getZoneFromTzCode } from "..";
import { addDays, addWeeks, addMonths, isBefore, isSameDay, parseISO } from "date-fns";

export const mapRawEventsToModel = (rawEvents: any[]): EventModelT[] => {
  return rawEvents.map((e) => ({
    _id: e._id,
    businessName: e.businessName || "",
    calendarName: e.calendarName || "",
    calId: e.calId,
    logo: e.logo || "",
    title: e.title || "",
    allDayEvent: !!e.allDayEvent,
    start: e.start,
    end: e.end,
    repeat: !!e.repeat,
    frequency: e.frequency || "Never",
    repeatEvery: e.repeatEvery ?? 1,
    days: e.days || [],
    repeatEndDate: e.repeatEndDate || "",
    location: e.location || "",
    link: e.link || "",
    picture: e.picture || "",
    youtube: e.youtube || "",
    restrict: e.restrict || [],
    zone: getZoneFromTzCode(e.zone?.tzCode || "Asia/Kolkata"),
    attach: e.attach || "",
    description: e.description || "",
    reminderSelected: e.reminderSelected || "",
    reminder: e.reminder || "",
    personalReminder: e.personalReminder || "",
    notifyBefore: e.notifyBefore || "",
    event: !!e.event,
    note: !!e.note,
    color: e.color || "#98A4B9",
    userType: e.userType || "businessUser",
    textColor: e.textColor || "#000000",
    deleted: !!e.deleted,
    blocked: !!e.blocked,
    createdAt: e.createdAt || new Date().toISOString(),
    Impression: e.Impression || [],
    Rsvps: e.Rsvps || [],
    Comments: e.Comments || [],
    Reply: e.Reply || [],
    usrId: e.usrId,
    Agenda: e.Agenda || [],
    profileImage: e.profileImage || null,
  }));
};

export const rawEventFormToModel = (e: any): EventModelT => {
  return {
    _id: e._id,
    businessName: e.businessName || "",
    calendarName: e.calendarName || "",
    calId: e.calId,
    logo: e.logo || "",
    title: e.title || "",
    allDayEvent: !!e.allDayEvent,
    start: e.start,
    end: e.end,
    repeat: !!e.repeat,
    frequency: e.frequency || "Never",
    repeatEvery: e.repeatEvery ?? 1,
    days: e.days || [],
    profileImage: e.profileImage || null,
    repeatEndDate: e.repeatEndDate || "",
    location: e.location || "",
    link: e.link || "",
    picture: e.picture || "",
    youtube: e.youtube || "",
    restrict: e.restrict || [],
    zone: getZoneFromTzCode(e.zone?.tzCode),
    attach: e.attach || "",
    description: e.description || "",
    reminderSelected: e.reminderSelected || "",
    reminder: e.reminder || "",
    personalReminder: e.personalReminder || "",
    notifyBefore: e.notifyBefore || "",
    event: !!e.event,
    note: !!e.note,
    color: e.color || "#98A4B9",
    userType: e.userType || "businessUser",
    textColor: e.textColor || "#000000",
    deleted: !!e.deleted,
    blocked: !!e.blocked,
    createdAt: e.createdAt || new Date().toISOString(),
    Impression: e.Impression || [],
    Rsvps: e.Rsvps || [],
    Comments: e.Comments || [],
    Reply: e.Reply || [],
    usrId: e.usrId,
    Agenda: e.Agenda || [],
  };
};

// Expands recurring events into individual event instances for display
export function expandRecurringEvents(events: EventModelT[]): EventModelT[] {
  const expanded: EventModelT[] = [];

  events.forEach(event => {
    if (event.repeat && event.frequency && event.repeatEndDate) {
      let current = parseISO(event.start);
      const end = parseISO(event.repeatEndDate);

      while (isBefore(current, end) || isSameDay(current, end)) {
        expanded.push({
          ...event,
          start: current.toISOString(),
          end: (() => {
            const duration = parseISO(event.end).getTime() - parseISO(event.start).getTime();
            return new Date(current.getTime() + duration).toISOString();
          })(),
        });

        if (event.frequency === "Daily") {
          current = addDays(current, event.repeatEvery || 1);
        } else if (event.frequency === "Weekly") {
          current = addWeeks(current, event.repeatEvery || 1);
        } else if (event.frequency === "Monthly") {
          current = addMonths(current, event.repeatEvery || 1);
        } else {
          break; // Unknown frequency
        }
      }
    } else {
      expanded.push(event);
    }
  });

  return expanded;
}
