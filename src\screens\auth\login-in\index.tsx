import { toast } from "sonner";
import { Link, Navigate, useNavigate } from "react-router";
import { FormEvent, useState } from "react";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import GmailPng from "@/assets/images/Gmail.png";
import ApplePng from "@/assets/images/apple.png";
import BgImage from "@/assets/images/bg-image.png";
import FacebookPng from "@/assets/images/facebook.png";
import {
  useAppleLogin,
  useEmailLogin,
  useFacebookLogin,
  useGoogleLogin,
  useLoginAccount,
} from "@/api/auth";
import BussinessLogo from "@/assets/images/fragment-business-logo.png";
import { useAuth } from "@/context/AuthContext";
import { User } from "firebase/auth";
import { extractFriendlyMessage } from "@/utils";

export default function LoginScreen() {
  const { userId, handleLogin } = useAuth();
  const navigate = useNavigate();

  const [email, setEmail] = useState<string>("");
  const [password, setPassword] = useState<string>("");

  const emailLogin = useEmailLogin();
  const googleLogin = useGoogleLogin();
  const appleLogin = useAppleLogin();
  const facebookLogin = useFacebookLogin();
  const loginAccount = useLoginAccount();

  const handleLoginFlow = async (loginFn: () => Promise<User>) => {
    try {
      const user = await loginFn();
      console.log("Login result", user);

      const loginResponse = await loginAccount.mutateAsync(user);

      if ("accData" in loginResponse) {
        if (loginResponse.accData.length > 0) {
          handleLogin(loginResponse.userData._id);
        } else {
          toast.info("No account details found");
          navigate("/signup-details", {
            state: {
              userId: loginResponse.userData._id,
              accoutData: null,
            },
          });
        }
      } else {
        navigate("/signup");
      }
    } catch (error) {
      let errorMessage = "Failed to login";

      if (error instanceof Error) {
        errorMessage = extractFriendlyMessage(error.message);
      }

      toast.error(errorMessage);
    }
  };

  const handleEmailLogin = async (e: FormEvent) => {
    e.preventDefault();
    await handleLoginFlow(() => emailLogin.mutateAsync({ email, password }));
  };

  const handleGoogleLogin = async () => {
    await handleLoginFlow(() => googleLogin.mutateAsync());
  };

  const handleAppleLogin = async () => {
    await handleLoginFlow(() => appleLogin.mutateAsync());
  };

  const handleFacebookLogin = async () => {
    await handleLoginFlow(() => facebookLogin.mutateAsync());
  };

  const isLoading =
    emailLogin.isPending ||
    googleLogin.isPending ||
    appleLogin.isPending ||
    facebookLogin.isPending ||
    loginAccount.isPending;

  if (userId) {
    return <Navigate to="/" replace />;
  }

  return (
    <div className="bg-fb-bPrime-600 flex flex-col md:flex-row w-full min-h-screen relative overflow-y-auto md:items-center md:justify-center">
      <img
        src={BgImage}
        alt=""
        className="absolute z-0 w-full h-full opacity-20"
      />
      <div className="flex justify-center items-center w-full md:w-1/2 z-10 pt-8 pb-4 md:py-0">
        <div className="flex flex-col items-center text-center">
          <div className="w-24 h-24 md:w-48 md:h-48">
            <img
              src={BussinessLogo}
              alt=""
              className="w-full h-full object-cover"
            />
          </div>
          <p className="text-xl md:text-3xl font-bold text-white">Connect with</p>
          <p className="text-2xl md:text-[44px] leading-none font-bold text-white">
            Everyone
          </p>
        </div>
      </div>
      <div className="flex flex-col md:justify-center h-auto md:h-full p-4 md:p-6 w-full md:w-1/2 z-10 ">
        <div className="bg-white flex flex-col w-full max-w-3xl rounded-3xl md:rounded-[56px] p-8 md:p-16 justify-center items-center overflow-y-auto drop-shadow-cardOutShadow shadow-cardInnerShadow">
          <form
            className="flex flex-col gap-8 w-full justify-center"
            onSubmit={handleEmailLogin}>
            <div className="flex flex-col gap-3 w-full">
              <div className="flex flex-col gap-1 w-full">
                <Label
                  htmlFor="email"
                  className="text-black font-semibold text-lg md:text-2xl">
                  Email Id*
                </Label>
                <Input
                  required
                  id="email"
                  value={email || ""}
                  type="email"
                  placeholder="<EMAIL>"
                  onChange={(e) => setEmail(e.target.value)}
                  className="border-black bg-fb-neutral-50"
                />
              </div>
              <div className="flex flex-col gap-1 w-full">
                <Label
                  htmlFor="password"
                  className="text-black font-semibold text-2xl">
                  Password*
                </Label>
                <Input
                  required
                  id="password"
                  value={password || ""}
                  type="password"
                  placeholder="********"
                  onChange={(e) => setPassword(e.target.value)}
                  className="border-black bg-fb-neutral-50"
                />
              </div>
            </div>
            <div className="flex flex-col gap-4 w-full">
              <div className="flex flex-col gap-1 w-full">
                <Button
                  disabled={isLoading}
                  type="submit"
                  className="text-xl font-semibold rounded-full text-white bg-fb-bPrime-600">
                  Log In
                </Button>
                <div className="flex items-center justify-between px-3">
                  <p className="text-black font-light text-base">
                    Do not have an account?{" "}
                    <Link to={"/signup"} className="text-fb-bPrime-600 font-semibold hover:underline">
                      Sign Up
                    </Link>
                  </p>
                  <Link to={"#"} className="text-fb-bPrime-600 font-semibold hover:underline">
                    Forgot Password
                  </Link>
                </div>
              </div>
              <div className="flex justify-center items-center gap-2 opacity-50">
                <div className="h-0.5 w-7 bg-black rounded-full mt-1" />
                or <div className="h-0.5 w-7 bg-black rounded-full mt-1" />
              </div>
              <div className="flex flex-col gap-2 w-full items-center">
                <p className="text-black">Log in with</p>
                <div className="flex flex-wrap items-center justify-center gap-2 w-full">
                  <Button
                    type="button"
                    className="drop-shadow-buttonShadow font-semibold w-fit bg-fb-neutral-50 flex text-black  gap-1 hover:bg-neutral-100 rounded-full"
                    onClick={handleGoogleLogin}>
                    <img src={GmailPng} alt="gmail" className="p-1.5 h-10" />
                    Gmail
                  </Button>
                  <Button
                    type="button"
                    className="drop-shadow-buttonShadow font-semibold w-fit bg-fb-neutral-900 flex justify-center items-center text-white  gap-1 hover:bg-neutral-800 rounded-full"
                    onClick={handleAppleLogin}>
                    <img src={ApplePng} alt="apple" className="p-1.5 h-10" />
                    Apple
                  </Button>
                  <Button
                    type="button"
                    className="drop-shadow-buttonShadow font-semibold w-fit bg-fb-neutral-50 flex text-black  gap-1 hover:bg-neutral-100 rounded-full"
                    onClick={handleFacebookLogin}>
                    <img
                      src={FacebookPng}
                      alt="facebook"
                      className="p-1.5 h-10"
                    />
                    Facebook
                  </Button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
