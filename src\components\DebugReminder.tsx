// DebugReminder.tsx
import { useState } from 'react';
import { reminderScheduler } from '../services/ReminderScheduler'; // Adjust path as needed

const DebugReminder = () => {
  const [logs, setLogs] = useState<string[]>([]);
  
  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testFullFlow = () => {
    addLog(' Testing full reminder flow...');
    
    // Test 1: Direct scheduler test
    const futureTime = new Date(Date.now() + 15000).toISOString(); // 15 seconds
    addLog(` Scheduling for: ${new Date(futureTime).toLocaleString()}`);
    
    const reminderId = reminderScheduler.scheduleReminder(
      'debug-test-123',
      'Debug Test Event',
      futureTime,
      '5-min'
    );
    
    addLog(`✅ Scheduled reminder: ${reminderId}`);
    addLog(` Active reminders: ${reminderScheduler.getScheduledReminders().length}`);
    
    // Test 2: Check localStorage
    const stored = localStorage.getItem('scheduledReminders');
    addLog(` Stored in localStorage: ${stored ? 'Yes' : 'No'}`);
  };

  return (
    <div style={{ border: '1px solid #ccc', padding: '10px', margin: '20px', borderRadius: '5px' }}>
      <h3>Reminder Debug Panel</h3>
      <button onClick={testFullFlow} style={{ marginRight: '10px', padding: '8px 12px', cursor: 'pointer' }}>Test Full Flow</button>
      <button onClick={() => setLogs([])} style={{ padding: '8px 12px', cursor: 'pointer' }}>Clear Logs</button>
      <div style={{ marginTop: '10px', maxHeight: '200px', overflowY: 'auto', background: '#f9f9f9', padding: '5px', border: '1px solid #eee' }}>
        {logs.map((log, index) => (
          <p key={index} style={{ margin: '2px 0', fontSize: '0.9em' }}>{log}</p>
        ))}
      </div>
    </div>
  );
};

export default DebugReminder;
