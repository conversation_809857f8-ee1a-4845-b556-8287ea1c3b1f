import { add, format } from "date-fns";

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";

import { TimePicker12H } from "../time-picker-12h";

type TimePickerProps = {
  date: Date | undefined;
  setDate: (date: Date) => void;
  isSmallView: boolean;
  showDay?: boolean;
  disableTimeSelection?: boolean;
};

export function DateTimePicker({
  date,
  setDate,
  isSmallView,
  showDay = true,
  disableTimeSelection = false,
}: TimePickerProps) {
  /**
   * carry over the current time when a user clicks a new day
   * instead of resetting to 00:00
   */
  const handleSelect = (newDay: Date | undefined) => {
    if (!newDay) return;
    if (!date) {
      setDate(newDay);
      return;
    }
    const diff = newDay.getTime() - date.getTime();
    const diffInDays = diff / (1000 * 60 * 60 * 24);
    const newDateFull = add(date, { days: Math.ceil(diffInDays) });
    setDate(newDateFull);
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant={"ghost"}
          className={cn(
            "h-auto rounded-xl justify-start text-left font-normal",
            !date && "text-muted-foreground"
          )}>
          {/* <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? format(date, "PPP HH:mm:ss") : <span>Pick a date</span>} */}
          {isSmallView ? (
            disableTimeSelection ? (
              <div className="justify-center items-center flex flex-col gap-0">
                <p className="font-semibold text-base md:text-lg leading-3">
                  {format(date || new Date(), "dd MMM''yy")}
                </p>
                {showDay && (
                  <p className=" text-xxs text-black/70 leading-3">
                    {format(date || new Date(), "EEEE")}
                  </p>
                )}
              </div>
            ) : (
              <div className="justify-center items-center flex flex-col gap-0">
                <p className="font-semibold text-lg md:text-xl leading-3">
                  {format(date || new Date(), "hh:mm")}
                  <span className="text-black/70 text-xs md:text-sm pl-0.5">
                    {format(date || new Date(), "aaa")}
                  </span>
                </p>
                <p className="font-semibold text-xs  md:text-base text-black/70 leading-6">
                  {format(date || new Date(), "dd MMM''yy")}
                </p>
                {showDay && (
                  <p className=" text-xs text-black/70 leading-3">
                    {format(date || new Date(), "EEEE")}
                  </p>
                )}
              </div>
            )
          ) : (
            <div className="justify-center items-center flex gap-4">
              <div className="text-center">
                <p className="font-semibold text-base  md:text-lg text-black/70">
                  {format(date || new Date(), "dd MMM''yy")}
                </p>
                <p className="font-normal text-xs md:text-sm text-black/70">
                  {format(date || new Date(), "EEEE")}
                </p>
              </div>
              <p className="font-bold text-lg md:text-2xl">@</p>
              <p className="font-semibold text-lg md:text-2xl">
                {format(date || new Date(), "hh:mm")}
                {showDay && (
                  <span className="text-black/70 text-xs md:text-sm pl-0.5">
                    {format(date || new Date(), "aaa")}
                  </span>
                )}
              </p>
            </div>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        <Calendar
          mode="single"
          selected={date}
          onSelect={(d) => handleSelect(d)}
          initialFocus
        />
        {!disableTimeSelection && (
          <div className="p-3 border-t border-border">
            <TimePicker12H setDate={setDate} date={date} />
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
}