//date-wise-group-events
import { CalendarModelT, EventModelT, TodoListModelT } from "@/types";
import { isToday } from "date-fns";
import { Link } from "react-router";

import { Button } from "@/components/ui/button";
import { SquarePlus } from "@/assets/svg/icons";
import { formatEventDate } from "@/utils";
import NoEventNote from "@/assets/images/notes-gray-nil.png";

import { EventListCardView, TodoCardView } from "../card-view";

type GroupedModelT = {
  date: string;
  todos: TodoListModelT[];
  events: EventModelT[];
};

const groupDataByDate = (
  events: EventModelT[],
  todos: TodoListModelT[],
  isPublicView?: boolean
): GroupedModelT[] => {
  const groupedMap = new Map<
    string,
    { todos: TodoListModelT[]; events: EventModelT[] }
  >();

  const today = new Date();
  today.setHours(0, 0, 0, 0); // Normalize today to start of day

  // Group todos - only upcoming for both views
  for (const todo of todos) {
    const todoDate = new Date(todo.date);
    todoDate.setHours(0, 0, 0, 0); // Normalize todo date to start of day
    const dateKey = todoDate.toDateString();

    if (todoDate.getTime() >= today.getTime()) {
      if (!groupedMap.has(dateKey)) {
        groupedMap.set(dateKey, { todos: [], events: [] });
      }
      groupedMap.get(dateKey)!.todos.push(todo);
    }
  }

  // Group events - only upcoming for both views
  for (const event of events) {
    const eventStartDate = new Date(event.start);
    eventStartDate.setHours(0, 0, 0, 0); // Normalize event start date to start of day
    const dateKey = eventStartDate.toDateString();

    if (eventStartDate.getTime() >= today.getTime()) {
      if (!groupedMap.has(dateKey)) {
        groupedMap.set(dateKey, { todos: [], events: [] });
      }
      groupedMap.get(dateKey)!.events.push(event);
    }
  }

  // Ensure today is included even if empty
  if (!isPublicView) {
    const todayKey = new Date().toDateString();
    if (!groupedMap.has(todayKey)) {
      groupedMap.set(todayKey, { todos: [], events: [] });
    }
  }

  // Convert Map to array and sort by date
  const groupedArray = Array.from(groupedMap.entries())
    .map(([date, { todos, events }]) => ({
      date,
      todos,
      events,
    }));

  if (isPublicView) {
    // For public view: show upcoming events with most recent first (today, tomorrow, etc.)
    return groupedArray.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  } else {
    // For private view: ascending order (earliest first)
    return groupedArray.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }
};

export default function DateGroupEventList({
  events,
  Todos,
  calendar,
  isPublicView = false,
}: {
  events: EventModelT[];
  Todos: TodoListModelT[];
  calendar: CalendarModelT | null;
  isPublicView?: boolean;
}) {
  // const groupedEvents = groupEventsByDate(events);
  const combileTodoAndEvent = groupDataByDate(events, Todos, isPublicView);

  // Check if there are no upcoming events/todos for public view
  const hasNoUpcomingData = combileTodoAndEvent.length === 0 || 
    combileTodoAndEvent.every(row => row.events.length === 0 && row.todos.length === 0);

  if (isPublicView && hasNoUpcomingData) {
    return <NoUpcomingEvents />;
  }

  return (
    <div className="flex flex-col gap-3 ">
      {combileTodoAndEvent.map((row, index) => {
        const rawDate = new Date(row.date);
        const isCurrentDay = isToday(rawDate);
        const date = formatEventDate(row.date);

        return (
          <div key={index} className="flex flex-col gap-2 mb-1">
            <div className="flex gap-1 items-center justify-between border-b border-fb-bPrime-hgts px-2">
              <div className="flex gap-1 items-end">
                <p className="font-semibold text-base">{date.date},</p>
                <p className="font-light text-xs">({date.day})</p>
              </div>
              {isCurrentDay && <p className="font-semibold text-sm">Today</p>}
            </div>
            {row.todos.map((item, index) => {
              return <TodoCardView {...item} key={index} />;
            })}
            {row.events.length === 0 && isCurrentDay && !isPublicView ? (
              <NoEventsForToday />
            ) : (
              row.events.map((item, index) => {
                return (
                  <EventListCardView
                    {...item}
                    key={index}
                    picture={calendar?.picture || item.picture}
                    repeat={item.repeat}
                    isPublicView={isPublicView}
                  />
                );
              })
            )}
          </div>
        );
      })}
    </div>
  );
}

const NoEventsForToday = () => {
  return (
    <div className="flex flex-col gap-1 justify-center items-center">
      <div className="w-32 lg:w-40 lg:h-40 mx-auto">
        <img
          src={NoEventNote}
          alt=""
          className="w-full h-full object-contain"
        />
      </div>
      <Link to={"/add-event"}>
        <Button
          size={"sm"}
          className="bg-fb-bPrime-600 text-xs py-1 text-white hover:bg-fb-bPrime-500 rounded-full px-4 h-7 drop-shadow-buttonShadow">
          <SquarePlus className="!size-4" />
          Add event Today
        </Button>
      </Link>
    </div>
  );
};

const NoUpcomingEvents = () => {
  return (
    <div className="flex flex-col gap-1 justify-center items-center py-8">
      <div className="w-32 lg:w-40 lg:h-40 mx-auto">
        <img
          src={NoEventNote}
          alt=""
          className="w-full h-full object-contain"
        />
      </div>
      <p className="text-gray-500 text-sm text-center">
        No Upcoming Events
      </p>
    </div>
  );
};