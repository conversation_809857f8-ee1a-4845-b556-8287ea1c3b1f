//public-calendar
import { useParams } from "react-router-dom";
import { useGetCalendarDetails } from "@/api/calendar";
import { useGetSingleCalenderEvents } from "@/api/event";
// import { useGetSimpleWebcalFeed } from "@/api/ics";
import { useGetAccounDetails } from "@/api/account";
import { Image } from "@/components/ui/image";
import AppLoader from "@/components/common/app-loader";
import DateGroupEventList from "@/components/common/event/date-wise-group-events";
import { useCalendarDetailsWithEvents } from "@/hook/useCalenderDetailsWithEvents";
import BgImage from "@/assets/images/bg-image.png";
import ErrorCalendar from "@/components/common/error-calendar";
import { Button } from "@/components/ui/button";
import Fragmentlogo from "@/assets/images/fragment-EU-logo.png";
import { BASE_URL , WEBSITE_URL } from "@/envs";
import { useState } from "react";
import GoogleCalendarIcon from "@/assets/images/google_calendar.png";
import AppleCalendarIcon from "@/assets/images/apple_calendar.png";
import { useAuth } from "@/context/AuthContext";

export default function PublicCalendartScreen() {
  const { id } = useParams<{ id: string }>();
  const [copyMessage, setCopyMessage] = useState("");
  const { userId } = useAuth();

  // useGetSimpleWebcalFeed(id || "");

  const {
    data: calendarData,
    isLoading: isCalendarLoading,
    error: calendarError,
  } = useGetCalendarDetails({
    calendarIds: id ? [id] : [],
  });

  const {
    data: eventsData,
    isLoading: areEventsLoading,
    error: eventsError,
  } = useGetSingleCalenderEvents({
    calendarIds: id ? [id] : [],
  });

  // Get account data for the calendar owner
  const calendar = calendarData?.[0];
  const {
    data: accountResponse,
    isLoading: isAccountLoading,
    error: accountError,
  } = useGetAccounDetails({
    userId: calendar?.usrId,
  });

  const { upComingEvents} = useCalendarDetailsWithEvents({
    calendarId: id,
    usrId: userId,
  });

  // Extract the first account from the response
  const accountData = accountResponse?.accData?.[0];

  const events = eventsData || [];

  if (isCalendarLoading || areEventsLoading || isAccountLoading) {
    return <AppLoader />;
  }

  if (calendarError || eventsError || accountError || !calendar || !accountData) {
    return <ErrorCalendar text="Account Not Found" />;
  }

  const handleAddToGoogleCalendar = () => {
    if (!id) return;
    
    // Remove protocol and use webcal format for Google Calendar
    const webcalUrl = `webcal://${BASE_URL.replace(/^https?:\/\//, "")}/api/ics/webcal/${id}`;
    const googleCalendarUrl = `https://calendar.google.com/calendar/render?cid=${encodeURIComponent(webcalUrl)}`;
    
    window.open(googleCalendarUrl, "_blank");
  };

  const handleAddToAppleCalendar = () => {
    if (!id) return;
    // Use webcal:// protocol to open directly in Apple Calendar
    const webcalUrl = `webcal://${BASE_URL.replace(/^https?:\/\//, "")}/api/ics/webcal/${id}`;
    
    // Try webcal first, fallback to https if webcal doesn't work
    try {
      window.location.href = webcalUrl;
    } catch (error) {
      // Fallback: open https version which user can manually add to calendar
      const httpsUrl = `${BASE_URL}/api/ics/webcal/${id}`;
      window.open(httpsUrl, "_blank");
    }
  };

  const handleDownloadIcs = async () => {
    if (!id) return;
    
    try {
      const response = await fetch(`${BASE_URL}/api/ics/${id}`);
      
      if (!response.ok) {
        throw new Error('Failed to download ICS file');
      }
      
      const icsContent = await response.text();
      const blob = new Blob([icsContent], { type: 'text/calendar' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${calendar?.calendarName || 'calendar'}.ics`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading ICS file:', error);
      setCopyMessage("Failed to download ICS file");
      setTimeout(() => setCopyMessage(""), 3000);
    }
  };

  const handleCopyIcsUrl = async () => {
    if (!id) return;
    
    const icsUrl = `${BASE_URL}/api/ics/${id}`;
    
    try {
      await navigator.clipboard.writeText(icsUrl);
      setCopyMessage("ICS URL copied to clipboard!");
      setTimeout(() => setCopyMessage(""), 3000);
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      const textArea = document.createElement('textarea');
      textArea.value = icsUrl;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        setCopyMessage("ICS URL copied to clipboard!");
        setTimeout(() => setCopyMessage(""), 3000);
      } catch (fallbackError) {
        console.error('Fallback copy failed:', fallbackError);
        setCopyMessage("Failed to copy URL");
        setTimeout(() => setCopyMessage(""), 3000);
      }
      document.body.removeChild(textArea);
    }
  };

  return (
    <div 
      className="min-h-screen bg-cover bg-center bg-no-repeat relative"
      style={{ backgroundImage: `url(${BgImage})` }}
    >
      {/* Background overlay */}
      <div className="absolute inset-0 bg-black/30"></div>
      
      {/* Content */}
      <div className="relative z-10">
        {/* Header */}
        <div className="bg-white/95 backdrop-blur-sm border-b px-4 sm:px-6 py-4 flex justify-between items-center">
          <div className="flex items-center gap-2 sm:gap-3">
            <div className="flex items-center gap-1">
              <Image 
                src={Fragmentlogo}
                alt="Fragment Logo" 
                className="w-8 h-8 sm:w-10 sm:h-10" 
              />
            </div>
            <span className="text-xs sm:text-sm text-gray-500 ml-1 sm:ml-2">Powered by</span>
            <span className="font-semibold text-gray-900 text-sm sm:text-base">Fragment</span>
          </div>
          <Button 
            onClick={() => window.open(WEBSITE_URL, "_blank")}
            variant="ghost"
            className="text-xs sm:text-sm font-medium text-gray-900 hover:text-gray-700 px-2 sm:px-4"
          >
            Visit Website
          </Button>
        </div>

        {/* Desktop Layout - Hidden on Mobile */}
        <div className="hidden lg:block">
          {/* Centered Calendar Title */}
          <div className="flex items-center justify-center py-12 px-6">
            <div className="flex items-center gap-8 max-w-4xl mx-auto">
              {/* Left side - Account Picture */}
              <div className="w-32 h-32 rounded-2xl overflow-hidden border-4 border-white shadow-xl flex-shrink-0">
                <Image
                  src={accountData.picture ?? undefined}
                  alt="Account Avatar"
                  className="w-full h-full object-cover"
                />
              </div>
              
              {/* Right side - Account Name and Business ID */}
              <div className="flex flex-col">
                <h1 className="text-5xl font-bold text-white mb-2 drop-shadow-lg">
                  {accountData.businessName}
                </h1>
                <p className="text-xl text-white/80 mb-6 drop-shadow-md">
                  @{accountData.businessId}
                </p>
                <Button 
                  onClick={() => window.location.href = `/calendar/public/all/${calendar.usrId}`}
                  variant="outline" 
                  className="border-white text-white hover:bg-white hover:text-gray-900 px-8 py-3 rounded-full font-medium bg-white/10 backdrop-blur-sm transition-all duration-300 shadow-lg w-fit"
                >
                  View All the Calendars
                </Button>
              </div>
            </div>
          </div>

          {/* Desktop Main Content Container */}
          <div className="max-w-[95%] mx-auto px-4 pb-8">
            <div className="bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl overflow-hidden">
              <div className="flex">
                {/* Left Panel - Now responsive to content height */}
                <div className="w-[450px] bg-gray-50/50 rounded-l-3xl flex flex-col">
                  <div className="flex-1 p-6">
                    <div className="bg-slate-800 text-white rounded-l-3xl p-8 shadow-xl h-full flex flex-col">
                    {/* Calendar Info Section */}
                    <div className="flex flex-col items-center mb-6">
                      <div className="w-20 h-20 mb-4 rounded-full overflow-hidden border-2 border-white/20 shadow-lg">
                        <Image
                          src={calendar.picture ?? undefined}
                          alt="Account Avatar"
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <h3 className="text-lg font-bold text-center mb-2">
                        {calendar.calendarName}
                      </h3>
                      <p className="text-sm text-gray-300 font-medium mb-3">
                        @{calendar.calendarId}
                      </p>
                      <p className="text-xs text-gray-300 text-center leading-relaxed">
                        {calendar.description}
                      </p>
                    </div>

                    {/* Separator Line */}
                    <div className="border-t border-slate-600/50 mb-6"></div>

                    {/* Add Calendar Section */}
                    <div className="bg-slate-700/50 rounded-2xl p-6 backdrop-blur-sm">
                      <p className="text-sm mb-6 font-medium text-center italic text-white">Add this calendar to your</p>
                      
                      {/* Fragment Button */}
                      <div className="bg-gradient-to-r from-cyan-400 to-cyan-500 text-slate-900 text-lg font-bold py-4 px-6 rounded-full text-center mb-6 shadow-lg flex items-center justify-center gap-3">
                        <Image 
                          src={Fragmentlogo}
                          alt="Fragment Logo" 
                          className="w-8 h-8" 
                        />
                        <span>Fragment</span>
                      </div>
                      
                      <p className="text-sm text-gray-300 leading-relaxed mb-8 text-center">
                        to get a chance to connect with organisers, RSVP, like events, make payments, and even create your own planning space.
                      </p>
                      
                      <p className="text-sm text-gray-300 mb-4 text-center italic">
                        You can also add calendar to your
                      </p>
                      
                      {/* Calendar Integration Buttons */}
                      <div className="grid grid-cols-2 gap-3 mb-6">
                        <Button 
                          onClick={handleAddToGoogleCalendar} 
                          className="bg-white hover:bg-gray-50 text-slate-900 text-sm font-semibold py-3 px-3 rounded-full shadow-lg border-0 flex items-center justify-center gap-2"
                        >
                          <Image 
                            src={GoogleCalendarIcon}
                            alt="Google Calendar" 
                            className="w-5 h-5" 
                          />
                          <span className="text-xs">Google Calendar</span>
                        </Button>
                        <Button 
                          onClick={handleAddToAppleCalendar} 
                          className="bg-white hover:bg-gray-50 text-slate-900 text-sm font-semibold py-3 px-3 rounded-full shadow-lg border-0 flex items-center justify-center gap-2"
                        >
                          <Image 
                            src={AppleCalendarIcon}
                            alt="Apple Calendar" 
                            className="w-5 h-5" 
                          />
                          <span className="text-xs">Apple Calendar</span>
                        </Button>
                      </div>
                      
                      {/* ICS Download Section */}
                      <div className="pt-4 border-t border-slate-600/50">
                        <p className="text-sm text-gray-300 mb-4 text-center">
                          Or download/share the calendar file
                        </p>
                        <div className="grid grid-cols-2 gap-3">
                          <Button 
                            onClick={handleDownloadIcs} 
                            className="bg-slate-600/50 hover:bg-slate-600/70 text-white text-xs font-medium py-3 px-2 rounded-full border border-slate-500/30 flex items-center justify-center gap-1"
                          >
                            📥 <span className="text-xs">Download ICS File</span>
                          </Button>
                          <Button 
                            onClick={handleCopyIcsUrl} 
                            className="bg-slate-600/50 hover:bg-slate-600/70 text-white text-xs font-medium py-3 px-2 rounded-full border border-slate-500/30 flex items-center justify-center gap-1"
                          >
                            🔗 <span className="text-xs">Copy ICS URL</span>
                          </Button>
                        </div>
                        {copyMessage && (
                          <p className="text-xs text-green-400 mt-3 text-center font-medium">
                            {copyMessage}
                          </p>
                        )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Right Panel - Now uses flex-1 to fill remaining space */}
                <div className="flex-1 bg-white/50 flex flex-col">
                  {/* Event List Header */}
                  <div className="px-8 py-6 border-b border-gray-200/50">
                    <div className="flex items-center justify-between">
                      <h2 className="text-2xl font-bold text-gray-900">Events</h2>
                    </div>
                  </div>

                  {/* Event List - This will expand to fill available space */}
                  <div className="flex-1 px-8 py-6 overflow-y-auto">
                    {events.length > 0 ? (
                      <DateGroupEventList
                        events={upComingEvents}
                        Todos={[]}
                        calendar={calendar}
                        isPublicView={true}
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <div className="text-center">
                          <ErrorCalendar text="No Upcoming Events" />
                          <p className="text-gray-500 mt-6 text-lg">No events found</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

          {/* Mobile Layout - Only shown on mobile */}
          <div className="lg:hidden min-h-screen">
            {/* Mobile Container */}
            <div className="min-h-screen">
              {/* Account Header Section */}
              <div className="px-4 py-6 pb-8 text-center from-slate-800 to-slate-700 text-white">
                <div className="w-16 h-16 mx-auto mb-3 rounded-sm overflow-hidden border-2 border-white/20 shadow-lg">
                  <Image
                    src={accountData.picture ?? undefined}
                    alt="Account Avatar"
                    className="w-full h-full object-cover"
                  />
                </div>
                <h1 className="text-xl font-bold mb-1">
                  {accountData.businessName}
                </h1>
                <p className="text-sm text-white/80 mb-6">
                  @{accountData.businessId}
                </p>
                <Button 
                  onClick={() => window.location.href = `/calendar/public/all/${calendar.usrId}`}
                  variant="outline" 
                  className="border-white text-white hover:bg-white hover:text-gray-900 px-4 py-2 rounded-full font-medium bg-white/10 backdrop-blur-sm text-sm mb-4"
                >
                  View All Calendars
                </Button>
              </div>

              {/* Calendar Info Card */}
            <div className="px-4 py-6 bg-white/95 backdrop-blur-sm rounded-t-3xl mt-6">
              <div className="mx-4 -mt-4 relative z-10">
                <div 
                  className="bg-slate-800 text-white shadow-xl overflow-hidden flex flex-col"
                  style={{
                    width: '361px',
                    height: '320px',
                    borderRadius: '14px',
                    padding: '8px',
                    gap: '8px',
                    maxWidth: '100%',
                    margin: '0 auto'
                  }}
                >
                  {/* Calendar Header */}
                  <div className="flex items-center gap-2 mb-1">
                    <div className="w-8 h-8 rounded-lg overflow-hidden border border-white/20 shadow-sm flex-shrink-0">
                      <Image
                        src={calendar.picture ?? undefined}
                        alt="Calendar Avatar"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-bold leading-tight truncate">
                        {calendar.calendarName}
                      </h3>
                      <p className="text-xs text-gray-300 truncate">
                        @{calendar.calendarId}
                      </p>
                    </div>
                  </div>
                  
                  {/* Description */}
                  <div className="mb-1">
                    <p className="text-xs text-gray-300 leading-tight">
                      Add this calendar to your Fragment to access Special features like interacting with the organisers and also creating your own planning space!
                    </p>
                  </div>

                  {/* Fragment Button */}
                  <div className="mb-1">
                    <div className="bg-gradient-to-r from-cyan-400 to-cyan-500 text-slate-900 text-sm font-bold py-1.5 px-3 rounded-lg text-center shadow-lg flex items-center justify-center gap-2">
                      <Image 
                        src={Fragmentlogo}
                        alt="Fragment Logo" 
                        className="w-4 h-4" 
                      />
                      <span>Fragment</span>
                    </div>
                  </div>
                  
                  {/* Alternative text */}
                  <div className="mb-1">
                    <p className="text-xs text-gray-300 text-center">
                      You can also add the calendar to other calendars
                    </p>
                  </div>
                  
                  {/* Calendar Integration Buttons */}
                  <div className="mb-1">
                    <div className="grid grid-cols-2 gap-1">
                      <Button 
                        onClick={handleAddToGoogleCalendar} 
                        className="bg-white hover:bg-gray-50 text-slate-900 text-xs font-medium py-1 px-1 rounded-lg shadow-sm border-0 flex items-center justify-center gap-1"
                      >
                        <Image 
                          src={GoogleCalendarIcon}
                          alt="Google Calendar" 
                          className="w-3 h-3" 
                        />
                        <span className="text-xs">Google Calendar</span>
                      </Button>
                      <Button 
                        onClick={handleAddToAppleCalendar} 
                        className="bg-white hover:bg-gray-50 text-slate-900 text-xs font-medium py-1 px-1 rounded-lg shadow-sm border-0 flex items-center justify-center gap-1"
                      >
                        <Image 
                          src={AppleCalendarIcon}
                          alt="Apple Calendar" 
                          className="w-3 h-3" 
                        />
                        <span className="text-xs">Apple Calendar</span>
                      </Button>
                    </div>
                  </div>
                  
                  {/* ICS Download Section */}
                  <div className="border-t border-slate-600/30 pt-1">
                    <p className="text-xs text-gray-300 mb-1 text-center">
                      You can get ICS file
                    </p>
                    <div className="grid grid-cols-2 gap-1">
                      <Button 
                        onClick={handleDownloadIcs} 
                        className="bg-slate-600/30 hover:bg-slate-600/50 text-white text-xs font-medium py-1 px-1 rounded-lg border border-slate-500/20 flex items-center justify-center gap-1"
                      >
                        <span className="text-xs">📥</span>
                        <span>download</span>
                      </Button>
                      <Button 
                        onClick={handleCopyIcsUrl} 
                        className="bg-slate-600/30 hover:bg-slate-600/50 text-white text-xs font-medium py-1 px-1 rounded-lg border border-slate-500/20 flex items-center justify-center gap-1"
                      >
                        <span className="text-xs"> 🔗</span>
                        <span>Copy</span>
                      </Button>
                    </div>
                    {copyMessage && (
                      <p className="text-xs text-green-400 mt-1 text-center font-medium">
                        {copyMessage}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Events Section */}
              
                <h2 className="text-xl font-bold text-gray-900 mb-4"></h2>
                {events.length > 0 ? (
                  <DateGroupEventList
                    events={upComingEvents}
                    Todos={[]}
                    calendar={calendar}
                    isPublicView={true}
                  />
                ) : (
                  <div className="text-center py-12 items-center">
                    <ErrorCalendar text="No Upcoming Events" />
                    <p className="text-gray-500 mt-4">No events found</p>
                  </div>
                )}
              </div>
            </div>
          </div>
      </div>
    </div>
  );
}