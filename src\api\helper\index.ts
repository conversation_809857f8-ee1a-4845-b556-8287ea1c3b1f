import { UploadImageResponseT } from "@/types";
import { StatusCodes } from "@/types/api";
import { API } from "@/utils/axios";
import { handleApiResponse } from "@/utils/axios/axiosHelper";
import { API_ENDPOINTS } from "../endpoints";

export const Api_Upload_Image = async (file: File | null): Promise<string> => {
  const FailureMessage = "Failed to upload image...";

  if (!file) return "";

  console.log(file, "--- file");

  const formData = new FormData();
  formData.append("file", file);

  try {
    const response = await API.post(
      API_ENDPOINTS.imageUpload.upload,
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );

    const result = handleApiResponse<UploadImageResponseT>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (result.status === StatusCodes.UNVERIFIED) {
      // Custom app-level status code — handle gracefully
      throw new Error("Image size is too large");
    }

    if (!result.success || !result.data || !result.data.publicUrl) {
      throw new Error(result.message || FailureMessage);
    }

    return result.data.publicUrl;
  } catch (error: unknown) {
    console.error("Image upload error:", error);
    throw error;
  }
};
