import React from 'react';

const WaveLoader: React.FC = () => {
  return (
    <div className="flex items-center justify-center space-x-2 p-4">
      <div className="flex space-x-1">
        <div 
          className="w-3 h-3 bg-cyan-400 rounded-full animate-bounce"
          style={{
            animationDelay: '0ms',
            animationDuration: '1000ms'
          }}
        ></div>
        <div 
          className="w-3 h-3 bg-cyan-400 rounded-full animate-bounce"
          style={{
            animationDelay: '150ms',
            animationDuration: '1000ms'
          }}
        ></div>
        <div 
          className="w-3 h-3 bg-cyan-400 rounded-full animate-bounce"
          style={{
            animationDelay: '300ms',
            animationDuration: '1000ms'
          }}
        ></div>
        <div 
          className="w-3 h-3 bg-cyan-400 rounded-full animate-bounce"
          style={{
            animationDelay: '450ms',
            animationDuration: '1000ms'
          }}
        ></div>
      </div>
    </div>
  );
};

export default WaveLoader;