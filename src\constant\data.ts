import { AddEventFieldValue, ProfileFieldValue } from "@/enums";

export const MonthNames = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

export const WeekDays = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

export const AddEventFields = [
  { name: "Location", value: AddEventFieldValue.LOCATION },
  { name: "<PERSON>minder", value: AddEventFieldValue.REMINDER },
  { name: "Repeat", value: AddEventFieldValue.REPEAT },
  { name: "Description", value: AddEventFieldValue.DESCRIPTION },
  { name: "Attachments", value: AddEventFieldValue.ATTACHMENTS },
  { name: "Responses", value: AddEventFieldValue.RESPONSES },
];

export const ProfileFields = [
  { name: "Details", value: ProfileFieldValue.DETAILS, toTrasfer: false },
  { name: "Setting<PERSON>", value: ProfileFieldValue.SETTINGS, toTrasfer: false },
  {
    name: "Terms and Condition",
    value: ProfileFieldValue.TERMS_AND_CONDITION,
    toTrasfer: true,
    to: "#",
  },
  {
    name: "Privacy Policy",
    value: ProfileFieldValue.PRIVACT_POLICY,
    toTrasfer: true,
    to: "#",
  },
  {
    name: "Sync All Devices",
    value: ProfileFieldValue.SyncAllDevices,
    toTrasfer: false,
  },
];

export const ReviewFileds = [
  "Privacy Concerns",
  "Unnecessary or Unused",
  "Security Concerns",
  "Not Satisfied with the Product",
  "Switching Platforms",
  "Limited Storage Space",
  "Ethics and Ideological Reasons",
  "Consumes too Much Battery",
  "Subscription Cost Issues",
  "Performance Issues",
];

export const ReminderTimes = [
  { value: "5 min", label: "5 min" },
  { value: "15 min", label: "15 min" },
  { value: "30 min", label: "30 min" },
  { value: "60 min", label: "60 min" },
];

export const RepeatDrop = [
  { value: "Never", label: "Never" },
  { value: "Daily", label: "Daily" },
  { value: "Weekly", label: "Weekly" },
  { value: "Monthly", label: "Monthly" },
];

export const RepeatDays = [
  { value: "Sunday", label: "Subday" },
  { value: "Monday", label: "Monday" },
  { value: "Tuesday", label: "Tuesday" },
  { value: "Wednesday", label: "Wednesday" },
  { value: "Thursday", label: "Thursday" },
  { value: "Friday", label: "Friday" },
  { value: "Saterday", label: "Saterday" },
];

export const RepeatDates = Array.from({ length: 365 }).map((_, i) => ({
  value: (i + 1).toString(),
  label: i + 1,
}));

export const Query_Key = {
  allCalendar: "allCalendar",
  calendarDetails: "calendarDetails",
  accountDetails: "accountDetails",
  allEventsCalendar: "allEventsCalendar",
  eventDetails: "eventDetails",
  allNotifications: "allNotifications",
};

export const firebaseErrorMap: Record<string, string> = {
  "auth/user-not-found": "User not found. Please check your email.",
  "auth/wrong-password": "Incorrect password. Please try again.",
  "auth/too-many-requests": "Too many login attempts. Please try again later.",
  "auth/invalid-email": "Invalid email address. Please check and try again.",
  "auth/email-already-in-use":
    "This email is already registered. Try logging in.",
  "auth/weak-password": "Password is too weak. Please use a stronger password.",
  "auth/operation-not-allowed": "This authentication method is not enabled.",
  "auth/user-disabled": "This account has been disabled. Contact support.",
  "auth/account-exists-with-different-credential":
    "An account already exists with the same email using a different login method.",
  "auth/popup-closed-by-user":
    "The popup was closed before completing the sign in.",
  "auth/popup-blocked":
    "Sign in popup was blocked. Please allow popups in your browser.",
  "auth/credential-already-in-use":
    "This credential is already associated with another account.",
  "auth/invalid-credential":
    "The provided credential is malformed or has expired.",
  "auth/missing-email": "Email is required but was not provided.",
  "auth/missing-password": "Password is required but was not provided.",
  "auth/internal-error": "An internal error occurred. Please try again later.",
  "auth/network-request-failed": "Network error. Please check your connection.",
  "auth/requires-recent-login":
    "This operation is sensitive and requires recent login. Please re-authenticate.",
};
