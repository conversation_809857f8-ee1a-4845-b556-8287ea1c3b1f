import { useState } from "react";

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ReviewFileds } from "@/constant/data";
import { Checkbox } from "@/components/ui/checkbox";
import { useDeleteAccount } from "@/api/auth";
import { useAuth } from "@/context/AuthContext";

export function DeleteProfileReviewPopup() {
  const [open, setOpen] = useState(false);

  const { userId } = useAuth();

  const [deleteAccount, setDeleteAccount] = useState({
    delete: false,
    review: false,
  });

  const { mutate, isPending: isDeleteAccountPending } = useDeleteAccount();

  const handleClose = (val: boolean) => {
    setOpen(val);
    setDeleteAccount({
      delete: false,
      review: false,
    });
  };

  const handleDeleteAccount = () => {
    if (!userId) return;

    mutate(userId, {
      onSuccess: () => {
        handleClose(false);
      },
    });
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogTrigger asChild>
        <Button className="ml-auto text-black bg-fb-warn-100 h-8 hover:bg-fb-warn-100/80 drop-shadow-buttonShadow">
          Delete Account
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px] min-h-56 !rounded-xl">
        <DialogHeader>
          <DialogTitle></DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
        {!deleteAccount.delete ? (
          <div className="flex flex-col gap-3 py-4">
            <p className="text-center text-lg lg:text-2xl leading-6 font-semibold">
              Are you sure you want to Delete your Account?
            </p>
            <div className="flex justify-center items-center gap-2 mt-8">
              <Button
                onClick={() => {
                  handleClose(false);
                }}
                className={cn(
                  "w-40  rounded-lg bg-fb-success-600 hover:bg-lime-700 drop-shadow-buttonShadow h-9"
                )}>
                No
              </Button>
              <Button
                onClick={() => {
                  setDeleteAccount({
                    delete: true,
                    review: false,
                  });
                }}
                className={cn(
                  "w-40  rounded-lg bg-fb-warn-600 hover:bg-red-700 drop-shadow-buttonShadow h-9"
                )}>
                Yes
              </Button>
            </div>
          </div>
        ) : deleteAccount.review ? (
          <div className="flex flex-col gap-3 py-4">
            <p className="text-center text-lg lg:text-2xl leading-6 font-semibold">
              Are you sure you want to Delete ?
            </p>
            <div className="flex justify-center items-center gap-2 mt-8">
              <Button
                onClick={() => {
                  handleClose(false);
                }}
                className={cn(
                  "w-40  rounded-lg bg-fb-success-600 hover:bg-lime-700 drop-shadow-buttonShadow h-9"
                )}>
                No
              </Button>
              <Button
                disabled={isDeleteAccountPending}
                onClick={handleDeleteAccount}
                className={cn(
                  "w-40  rounded-lg bg-fb-warn-600 hover:bg-red-700 drop-shadow-buttonShadow h-9"
                )}>
                Yes
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex flex-col gap-3 py-4">
            <p className="text-center text-lg lg:text-2xl leading-6 font-semibold">
              Sorry to have you leave. <br /> Can we know why you want to leave.
            </p>
            <div className="flex flex-col gap-2 px-20 py-6">
              {ReviewFileds.map((review, ind) => {
                return (
                  <div className="flex items-center gap-2" key={ind}>
                    <Checkbox />
                    <p>{review}</p>
                  </div>
                );
              })}
            </div>
            <div className="flex justify-end items-end gap-2 ">
              <Button
                onClick={() => {
                  setDeleteAccount((old) => ({ ...old, review: true }));
                }}
                className={cn(
                  "w-40  rounded-lg bg-fb-warn-600 hover:bg-red-700 drop-shadow-buttonShadow h-9"
                )}>
                Next
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
