import { API } from "@/utils/axios";
import { handleApiError, handleApiResponse } from "@/utils/axios/axiosHelper";
import { API_ENDPOINTS } from "../endpoints";
import {
  CreateCommentFormT,
  CreateSubCommentFormT,
  DeleteCommentFormT,
  DeleteSubCommentFormT,
  ReportCommentFormT,
  ReportSubCommentFormT,
  StatusCodes,
  UpdateCommentFormT,
} from "@/types/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Query_Key } from "@/constant/data";

export const useCreateComment = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: Api_Create_Event_Comment,
    onSuccess(_, { agendaId }) {
      queryClient.invalidateQueries({
        queryKey: [Query_Key.eventDetails, agendaId],
      });
    },
  });
};

export const useCreateSubComment = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: Api_Create_Comment_Replay,
    onSuccess(_, { agendaId }) {
      queryClient.invalidateQueries({
        queryKey: [Query_Key.eventDetails, agendaId],
      });
    },
  });
};

export const useUpdateComment = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: Api_Update_Comment,
    onSuccess(_, { agendaId }) {
      queryClient.invalidateQueries({
        queryKey: [Query_Key.eventDetails, agendaId],
      });
    },
  });
};

export const useDeleteComment = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: Api_Delete_Comment,
    onSuccess(_, { agendaId }) {
      queryClient.invalidateQueries({
        queryKey: [Query_Key.eventDetails, agendaId],
      });
    },
  });
};

export const useReportComment = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: Api_Report_Comment,
    onSuccess(_, { agendaId }) {
      queryClient.invalidateQueries({
        queryKey: [Query_Key.eventDetails, agendaId],
      });
    },
  });
};

export const useReportSubComment = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: Api_Report_Sub_Comment,
    onSuccess(_, { agendaId }) {
      queryClient.invalidateQueries({
        queryKey: [Query_Key.eventDetails, agendaId],
      });
    },
  });
};

export const useDeleteSubComment = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: Api_Delete_Sub_Comment,
    onSuccess(_, { agendaId }) {
      queryClient.invalidateQueries({
        queryKey: [Query_Key.eventDetails, agendaId],
      });
    },
  });
};

const Api_Create_Event_Comment = async (
  data: CreateCommentFormT
): Promise<{ _id: string }> => {
  const FailureMessage = "Failed to creat comment...";

  try {
    const response = await API.post(API_ENDPOINTS.comment.create, data);
    const result = handleApiResponse<{ _id: string }>(
      response,
      StatusCodes.CREATED,
      FailureMessage
    );

    if (!result.success || !result.data) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_Update_Comment = async (
  data: UpdateCommentFormT
): Promise<{ _id: string }> => {
  const FailureMessage = "Failed to update comment...";

  try {
    const response = await API.post(API_ENDPOINTS.comment.edit, data);
    const result = handleApiResponse<{ _id: string }>(
      response,
      StatusCodes.OK,
      FailureMessage
    );

    if (!result.success || !result.data) {
      throw new Error(result.message || FailureMessage);
    }
    return result.data;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_Delete_Comment = async (
  data: DeleteCommentFormT
): Promise<boolean> => {
  const FailureMessage = "Failed to delete comment...";

  try {
    await API.post(API_ENDPOINTS.comment.delete, data);
    return true;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_Report_Comment = async (
  data: ReportCommentFormT
): Promise<boolean> => {
  const FailureMessage = "Failed to report comment...";

  try {
    await API.post(API_ENDPOINTS.comment.report, data);
    return true;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_Create_Comment_Replay = async (
  data: CreateSubCommentFormT
): Promise<boolean> => {
  const FailureMessage = "Failed to creat sub comment...";

  try {
    await API.post(API_ENDPOINTS.reply.create, data);

    return true;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_Report_Sub_Comment = async (
  data: ReportSubCommentFormT
): Promise<boolean> => {
  const FailureMessage = "Failed to report sub comment...";

  try {
    await API.post(API_ENDPOINTS.reply.report, data);
    return true;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};

const Api_Delete_Sub_Comment = async (
  data: DeleteSubCommentFormT
): Promise<boolean> => {
  const FailureMessage = "Failed to delete sub comment...";

  try {
    await API.post(API_ENDPOINTS.reply.delete, data);
    return true;
  } catch (error) {
    throw new Error(handleApiError(error, FailureMessage).message);
  }
};
